#!/usr/bin/env node

const { MongoClient } = require('mongodb')
const { join } = require('path')
const createConfigury = require('@clocklimited/configury')
const hat = require('hat')

const config = createConfigury(join(__dirname, '/../config.json'))(
  process.env.NODE_ENV
)

// Parse command line arguments
const args = process.argv.slice(2)

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Usage: node tools/update-article-layouts.js [options]

Description:
  Updates article layouts in sections to add standardized widget configurations.
  Updates sticky layouts with predefined widget sets for 'events' and 'default' sections.

Options:
  --dry-run        Show what would be changed without making actual updates
  --section <slug> Only process the specified section (by slug)
  --help, -h       Show this help message

Examples:
  node tools/update-article-layouts.js
  node tools/update-article-layouts.js --dry-run
  node tools/update-article-layouts.js --section events
  node tools/update-article-layouts.js --dry-run --section events
  `)
  process.exit(0)
}

const isDryRun = args.includes('--dry-run')
const sectionSlugIndex = args.indexOf('--section')
const targetSection =
  sectionSlugIndex !== -1 ? args[sectionSlugIndex + 1] : null

if (sectionSlugIndex !== -1 && !targetSection) {
  console.error('Error: --section requires a section slug argument')
  process.exit(1)
}

// Widget factory functions for better maintainability
const createBaseWidget = (type, additionalProps = {}) => ({
  id: hat(),
  type,
  className: null,
  visible: true,
  liveDate: null,
  expiryDate: null,
  tiers: [],
  displayOptions: ['desktop', 'tablet', 'mobile'],
  essential: true,
  ...additionalProps
})

const createAdvert = (
  size,
  alignment,
  displayOptions = ['desktop', 'tablet', 'mobile']
) =>
  createBaseWidget('advert', {
    displayOptions,
    slotName: null,
    size,
    suffix: null,
    background: false,
    keyValueTargeting: [],
    alignment,
    ...(alignment === 'right' && { titleColor: null, copyColor: null })
  })

// Widget configurations
const WIDGET_CONFIGS = {
  events: [
    createBaseWidget('articleLayoutRelatedEntities'),
    createBaseWidget('eventLatestCTA', {
      eventUmbrellaId: '',
      background: '',
      textOverride: '',
      videoId: ''
    }),
    createBaseWidget('magazineLatestCTA', {
      title: 'Read Now',
      categories: ['6da07148cec1639741802a854b607523']
    }),
    createAdvert('300x250', 'right'),
    createBaseWidget('articleLayoutEventInfo', {
      titleColor: null,
      copyColor: null
    }),
    createAdvert('300x250', 'centre'),
    createAdvert('300x600', 'right', ['desktop'])
  ],
  default: [
    createBaseWidget('eventLatestCTA', {
      eventUmbrellaId: '',
      background: '',
      textOverride: '',
      videoId: ''
    }),
    createBaseWidget('magazineLatestCTA', {
      title: 'Read Now',
      categories: ['6da07148cec1639741802a854b607523']
    }),
    createAdvert('300x250', 'right'),
    createBaseWidget('articleLayoutRelatedContent', {
      titleColor: null,
      copyColor: null
    }),
    createAdvert('300x250', 'centre'),
    createAdvert('300x600', 'right', ['desktop'])
  ]
}

const STICKY_LAYOUT_TITLE = '3:1 (Sticky, Narrow)'

const connectionUri =
  process.env.MONGO_URL ||
  process.env.NF_DATABASE_MONGO_SRV ||
  config.databaseUrl

function processSectionLayout(section) {
  let articleLayout = section.layouts?.article?.layout
  if (!articleLayout) return null

  const stickyLayoutIndex = articleLayout.findIndex(
    (row) => row.title === STICKY_LAYOUT_TITLE
  )

  if (stickyLayoutIndex === -1) {
    console.log(`Skipping section ${section.slug}: No sticky layout found`)

    return null
  }

  const stickyLayout = articleLayout[stickyLayoutIndex]
  if (!stickyLayout.cols?.[1]?.widgetArea) {
    console.log(
      `Skipping section ${section.slug}: Invalid sticky layout structure`
    )

    return null
  }

  // Get widgets based on section type
  const widgetConfig = section.slug === 'events' ? 'events' : 'default'
  const newWidgets = WIDGET_CONFIGS[widgetConfig].map((widget) => ({
    ...widget
  })) // Deep clone

  // Create the update operation
  const updatedLayouts = { ...section.layouts }
  if (section.slug !== 'events') {
    articleLayout = articleLayout.filter((row) =>
      [STICKY_LAYOUT_TITLE, 'Full-width (Tight)', 'Default (Narrow)'].includes(
        row.title
      )
    )

    articleLayout[stickyLayoutIndex].cols[0].widgetArea.widgets = articleLayout[
      stickyLayoutIndex
    ].cols[0].widgetArea.widgets.filter((widget) => {
      return ![
        'articleLayoutTags',
        'articleLayoutShare',
        'articleLayoutRelatedEntities'
      ].includes(widget.type)
    })

    articleLayout[stickyLayoutIndex].cols[0].widgetArea.widgets.push(
      createBaseWidget('articleLayoutRelatedEntities')
    )

    articleLayout[stickyLayoutIndex].cols[0].widgetArea.widgets.push(
      createBaseWidget('articleLayoutTags')
    )
  }

  updatedLayouts.article.layout = articleLayout // Use the filtered layout
  updatedLayouts.article.layout[
    stickyLayoutIndex
  ].cols[1].widgetArea.widgets = newWidgets

  return {
    updateOne: {
      filter: { _id: section._id },
      update: { $set: { layouts: updatedLayouts } }
    }
  }
}

// Main function
async function main() {
  let client

  try {
    console.log('Starting article layout migration...')

    if (isDryRun) {
      console.log('*** DRY RUN MODE - No changes will be made ***')
    }

    if (targetSection) {
      console.log(`Targeting specific section: ${targetSection}`)
    }

    client = await MongoClient.connect(connectionUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    })

    const db = client.db()

    // Build query based on CLI arguments
    const query = {
      'layouts.article.layout': { $exists: true }
    }

    if (targetSection) {
      query.slug = targetSection
    }

    const sections = await db.collection('section').find(query).toArray()

    if (sections.length === 0) {
      if (targetSection) {
        console.log(`No section found with slug: ${targetSection}`)
      } else {
        console.log('No sections with article layouts found')
      }

      return
    }

    console.log(`Found ${sections.length} section(s) to process`)

    const bulkOps = []
    let processedCount = 0

    for (const section of sections) {
      console.log(`\nProcessing section: ${section.slug}`)

      const result = processSectionLayout(section)
      if (result) {
        if (isDryRun) {
          console.log(`Would update section: ${section.slug}`)
          // In dry run, show what widgets would be added
          const widgetConfig = section.slug === 'events' ? 'events' : 'default'
          const widgets = WIDGET_CONFIGS[widgetConfig]
          console.log(`  Would add ${widgets.length} widgets:`)
          widgets.forEach((widget, index) => {
            console.log(
              `    ${index + 1}. ${widget.type} (${widget.alignment || 'N/A'})`
            )
          })
        } else {
          bulkOps.push(result)
        }
        processedCount++
      }
    }

    if (!isDryRun && bulkOps.length > 0) {
      console.log(`\nUpdating ${bulkOps.length} section(s)...`)
      await db.collection('section').bulkWrite(bulkOps)
      console.log(`Successfully updated ${processedCount} section(s)`)
    } else if (isDryRun) {
      console.log(
        `\nDry run complete. Would have updated ${processedCount} section(s)`
      )
    } else {
      console.log('No sections required updates')
    }

    console.log('\n=== MIGRATION COMPLETE ===')
  } catch (error) {
    console.error('Error during migration:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the main function
main().catch((error) => {
  console.error('Unhandled error:', error)
  process.exit(1)
})
