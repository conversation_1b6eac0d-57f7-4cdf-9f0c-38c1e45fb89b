import React from 'react'
import { object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/magazine-issue/render-magazine-issue'
import StandardMeta from '@/component/Meta/Standard'

import Layout from '@/component/Structure/Layout'
import getUserAgent from './lib/get-user-agent'
import appendUser from './lib/append-user'

const MagazineIssuePage = ({
  magazineIssue,
  section,
  instance,
  pageData,
  __url
}) => {
  const layout = section?.layouts?.magazine?.layout
  return (
    <>
      <StandardMeta
        title={magazineIssue.title}
        seoTitle={magazineIssue.title}
        canonicalUrl={pageData.url}
        images={
          magazineIssue.images.cover_321x446_321 &&
          magazineIssue.images.cover_321x446_321.length
            ? [
                {
                  url: `${magazineIssue.images.cover_321x446_321[0].url}.jpg`,
                  width: 321,
                  height: 446,
                  alt: magazineIssue.title
                }
              ]
            : [
                {
                  url: instance.darkLogoUrl,
                  alt: instance.name
                }
              ]
        }
      />
      <Layout
        layout={layout}
        __url={__url}
        section={section}
        instance={instance}
        pageData={pageData}
      />
    </>
  )
}

MagazineIssuePage.propTypes = {
  magazineIssue: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired
}

MagazineIssuePage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const instance = data?.response?.instance
  const magazineIssue = { ...data?.response?.resource }
  const section = magazineIssue?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts
  let pageData = { url, params, magazineIssue, section, isDesktop }
  pageData = await appendUser(pageData, context, instance)
  return { ...data.response, magazineIssue, section, pageData, context: ctx }
}

export default MagazineIssuePage
