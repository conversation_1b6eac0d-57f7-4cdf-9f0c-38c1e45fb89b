import React from 'react'
import { object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/company/render-company'
import StandardMeta from '@/component/Meta/Standard'

import Layout from '@/component/Structure/Layout'
import getUserAgent from './lib/get-user-agent'
import appendUser from './lib/append-user'

const CompanyPage = ({
  company,
  section,
  instance,
  pageData,
  __url,
  latestMagazineIssue
}) => {
  const layout = section?.layouts?.company?.layout

  return (
    <>
      <StandardMeta
        title={company.name}
        seoTitle={company.name}
        canonicalUrl={pageData.url}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={section}
        instance={instance}
        pageData={pageData}
        latestMagazineIssue={latestMagazineIssue}
      />
    </>
  )
}

CompanyPage.propTypes = {
  company: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired,
  latestMagazineIssue: object
}

CompanyPage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const instance = data?.response?.instance
  const company = { ...data?.response?.resource }
  const section = company?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts
  let pageData = { url, params, company, section }
  pageData = await appendUser(pageData, context, instance)
  return { ...data.response, company, section, pageData, context: ctx }
}

export default CompanyPage
