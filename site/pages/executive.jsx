import React from 'react'
import { object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/executive/render-executive'
import StandardMeta from '@/component/Meta/Standard'

import Layout from '@/component/Structure/Layout'
import getUserAgent from './lib/get-user-agent'
import appendUser from './lib/append-user'

const ExecutivePage = ({ executive, section, instance, pageData, __url }) => {
  const layout = section?.layouts?.executive?.layout

  return (
    <>
      <StandardMeta
        title={executive.name}
        seoTitle={executive.name}
        canonicalUrl={pageData.url}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={section}
        instance={instance}
        pageData={pageData}
      />
    </>
  )
}

ExecutivePage.propTypes = {
  executive: object,
  section: object,
  instance: object,
  pageData: object,
  __url: func.isRequired
}

ExecutivePage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const instance = data?.response?.instance
  const executive = { ...data?.response?.resource }
  const section = executive?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts
  let pageData = { url, params, executive, section }
  pageData = await appendUser(pageData, context, instance)
  return { ...data.response, executive, section, pageData, context: ctx }
}

export default ExecutivePage
