import React from 'react'
import { object, func } from 'prop-types'

import retrieveAndProcessDataForPage from '@/lib/data-for-page-processor-and-retriever'
import createRenderQuery from '@/query/author/render-author'
import StandardMeta from '@/component/Meta/Standard'

import Layout from '@/component/Structure/Layout'
import getUserAgent from './lib/get-user-agent'
import appendUser from './lib/append-user'

const AuthorPage = ({ author, section, pageData, __url, instance }) => {
  const layout = section?.layouts?.author?.layout

  return (
    <>
      <StandardMeta
        title={author?.name}
        seoTitle={author?.name}
        canonicalUrl={pageData.url}
      />
      <Layout
        layout={layout}
        __url={__url}
        section={section}
        pageData={pageData}
      />
    </>
  )
}

AuthorPage.propTypes = {
  author: object,
  section: object,
  pageData: object,
  instance: object,
  __url: func.isRequired
}

AuthorPage.getInitialProps = async (context) => {
  const params = context?.query
  const query = createRenderQuery()
  const data = await retrieveAndProcessDataForPage(context, query)
  if (data.error) return data
  const instance = data?.response?.instance
  const author = { ...data?.response?.resource }
  const section = author?.entitySection
  const { isDesktop } = getUserAgent(context)
  const ctx = { isDesktop }
  const url = data?.vars?.url
  delete data?.response?.resource?.layouts
  let pageData = { url, params, author, section }
  pageData = await appendUser(pageData, context, instance)
  return { ...data.response, author, section, pageData, context: ctx }
}

export default AuthorPage
