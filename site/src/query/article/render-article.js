import renderResource from '../resource/render-resource'
import body from './body'

import widgetArea from '../widget/widget-area'
import widget from '../widget/widget'
import articleLayoutHeader from '../widgets/general/article-layout-header'
import textWidget from '../widgets/general/text'
import blockquoteWidget from '../widgets/general/blockquote'
import advertWidget from '../widgets/general/advert'
import articleLayoutTags from '../widgets/general/article-layout-tags'
import articleGridWidget from '../widgets/general/article-grid'
import partnersWidget from '../widgets/general/partners'
import videoGridWidget from '../widgets/general/video-grid'
import carouselWidget from '../widgets/general/carousel'
import articleStackWidget from '../widgets/general/article-stack'
import articleLayoutShareWidget from '../widgets/general/article-layout-share'
import headerWidget from '../widgets/general/header'
import htmlWidget from '../widgets/general/html'
import articleLayoutStoryCtaWidget from '../widgets/general/article-layout-story-cta'
import articleLayoutEventInfo from '../widgets/general/article-layout-event-info'
import articleLayoutDownloadWidget from '../widgets/general/article-layout-download'
import articleLayoutStandfirstWidget from '../widgets/general/article-layout-standfirst'
import articleLayoutRelatedEntitiesWidget from '../widgets/general/article-layout-related-entities'
import articleLayoutEventRegistrationWidget from '../widgets/general/article-layout-event-registration'
import articleLayoutImagesWidget from '../widgets/general/article-layout-images'
import articleLayoutRelatedContentWidget from '../widgets/general/article-layout-related-content'
import magazineCta from '../widgets/general/magazine-cta'
import eventLatestCta from '@/query/widgets/general/event-latest-cta'
import magazineLatestCta from '@/query/widgets/general/magazine-latest-cta'

const articleLayoutWidgets = [
  widget(),
  articleLayoutHeader(),
  textWidget(),
  blockquoteWidget(),
  advertWidget(),
  articleLayoutTags(),
  articleGridWidget(),
  articleStackWidget(),
  videoGridWidget(),
  carouselWidget(),
  headerWidget(),
  htmlWidget(),
  articleLayoutShareWidget(),
  articleLayoutStoryCtaWidget(),
  articleLayoutEventInfo(),
  articleLayoutDownloadWidget(),
  articleLayoutStandfirstWidget(),
  articleLayoutImagesWidget(),
  articleLayoutRelatedContentWidget(),
  articleLayoutRelatedEntitiesWidget(),
  articleLayoutEventRegistrationWidget(),
  articleLayoutImagesWidget(),
  partnersWidget(),
  magazineCta(),
  eventLatestCta(),
  magazineLatestCta()
]

const imageProperties = `
  url
  caption
  link
`

export const articleGraphQL = `
  _id
  headline
  sell
  metaTitle
  metaDescription
  shareTitle
  shareDescription
  displayDate
  advertSlotName
  eventRegistrationLink
  issue
  issueSlug
  pageNumber
  contentType
  address
  fullUrlPath
  startDate
  endDate
  author {
    name
    slug
  }
  category
  eventId
  eventBaseSlug
  eventArticleCategoryKey
  subAttribution
  companies {
    _id
    name
    slug
    description
    shortDescription
    images {
      logo_free_127 {
        url
      }
    }
  }
  partners {
    _id
    name
    website
  }
  executives {
    name
    slug
    bio
    companyId
    images {
      headshot_220x347_220 {
        url
      }
    }
  }
  price
  downloadUrl
  tags {
    tag
  }
  signUpRequired
  images {
    thumbnail_landscape_322 {
      ${imageProperties}
    }
    thumbnail_landscape_138 {
      ${imageProperties}
    }
    thumbnail_landscape_206 {
      ${imageProperties}
    }
    thumbnail_landscape_290 {
      ${imageProperties}
    }
    thumbnail_landscape_330 {
      ${imageProperties}
    }
    thumbnail_landscape_412 {
      ${imageProperties}
    }
    thumbnail_landscape_580 {
      ${imageProperties}
    }
    thumbnail_landscape_900 {
      ${imageProperties}
    }
    thumbnail_portrait_144 {
      ${imageProperties}
    }
    thumbnail_portrait_286 {
      ${imageProperties}
    }
    thumbnail_portrait_576 {
      ${imageProperties}
    }
    thumbnail_widescreen_553 {
      ${imageProperties}
    }
    hero_landscape_320 {
      ${imageProperties}
    }
    hero_landscape_668 {
      ${imageProperties}
    }
    hero_landscape_900 {
      ${imageProperties}
    }
    hero_landscape_1336 {
      ${imageProperties}
    }
    hero_landscape_1800 {
      ${imageProperties}
    }
    hero_portrait_144 {
      ${imageProperties}
    }
    hero_widescreen_320 {
      ${imageProperties}
    }
    hero_widescreen_668 {
      ${imageProperties}
    }
    hero_widescreen_900 {
      ${imageProperties}
    }
    hero_widescreen_1336 {
      ${imageProperties}
    }
    hero_widescreen_1800 {
      ${imageProperties}
    }
    thumbnail_landscape_138 {
      ${imageProperties}
    }
    thumbnail_landscape_206 {
      ${imageProperties}
    }
    share_widescreen_1200 {
      ${imageProperties}
    }
  }
  videoProvider
  videoId
  issuPublicationId
  issuIssueId
  magazineOrigin
  slug
  section {
    _id
    slug
    advertSectionId
    keyValueTargeting {
      key
      value
    }
    layouts {
      article {
        layout {
          attributes
          cols {
            width
            attributes
            widgetArea {
              ${widgetArea(articleLayoutWidgets)}
            }
          }
        }
      }
    }
    setArticleIdAsKeyValuePair
    articleIdKeyToSet
  }
  migratedFromId
  canonicalUrl
  ${body()}
`

const createQuery = () => {
  const articleQuery = `...on Article {
    ${articleGraphQL}
  }`
  return renderResource(articleQuery)
}

export default createQuery
