const renderGenericFields = () => `
  enabledInstances {
    name
    subdomain
    brandType
  }
  instance(url:$url) {
    _id
    name
    theme
    brandType
    headerType
    primaryColorOverride
    secondaryColorOverride
    darkLogoUrl
    lightLogoUrl
    holdingPageTitle
    iconType
    enabled
    holdingPageSubtitle
    holdingImageAlt
    cookieConsentId
    pianoEnabled
    pianoApplicationId
    pianoApiToken
    pianoSubscriptionId
    pianoDmpSiteGroupId
    robotsTxt
    images {
      holding_landscape_1920 {
        url
      }
      holding_landscape_1048 {
        url
      }
      holding_portrait_400 {
        url
      }
      holding_portrait_600 {
        url
      }
      share_landscape_1920 {
        url
      }
      share_landscape_1048 {
        url
      }
    }
    subdomain
    strapline
    facebookId
    twitterId
    linkedinId
    youtubeId
    instagramId
    mediumId
    issuPublicationId
    editorialContactFormToEmailAddress
    salesforceId
    googleTagManagerId
    headerAdvertVisible
    preventAdsOnMobile
    headerAdvertBelowNav
    headerAdvertSlotName
    advertSiteId
    featureFlags
    keyValueTargeting {
      key
      value
    }
    googleOptimizeContainerId
    googleOptimizeAsynchronous
    navItems {
      title
      url
      type
      subItems {
        title
        type
        url
      }
    }
    footerNavItems {
      title
      url
      type
      subItems {
        title
        type
        url
      }
    }
    footerNavInstanceLinks {
      title
      url
    }
  }
  latestArticles(url:$url) {
    headline
    fullUrlPath
    eventId
    eventBaseSlug
    eventArticleCategoryKey
  }
  latestMagazineIssue(url:$url) {
    slug
    images {
      cover_321x446_150 {
        url
      }
      cover_321x446_300 {
        url
      }
    }
  }
  dmpSiteGroups {
    results
  }
`

export default renderGenericFields
