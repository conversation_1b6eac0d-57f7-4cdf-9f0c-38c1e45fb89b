import React from 'react'
import { string, node, bool } from 'prop-types'
import cx from 'classnames'
import Type from '../../Type'

import styles from '../RelatedEntities.module.scss'

const EntitiesContainer = ({ title, children, inArticleBody }) => {
  return (
    <div
      className={cx(
        styles.EntitiesContainer,
        inArticleBody && styles.EntitiesContainerInArticleBody
      )}
    >
      <Type
        size={['body2', 'body3']}
        weight="medium"
        uppercase
        className={styles.Title}
        as="h2"
      >
        {title}
      </Type>
      {children}
    </div>
  )
}

EntitiesContainer.propTypes = {
  title: string,
  children: node,
  inArticleBody: bool
}

export default EntitiesContainer
