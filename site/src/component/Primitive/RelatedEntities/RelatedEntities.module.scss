.EntitiesContainer {
  align-items: start;
  border-bottom: 2px solid $color-misc-divider;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding-bottom: spacing(3);

  //& + & {
  //  margin-top: spacing(2);
  //}

  @include mq($breakpoint-desktop) {
    padding-bottom: spacing(3);
  }
}

.EntitiesContainerInArticleBody {
  border-block: 1px solid currentColor;
  padding-block: 24px;
}

@media (min-width: 600px) {
  .EntitiesContainerInArticleBody {
    padding-block: 32px;
  }
}

.Entity {
  align-items: center;
  background-color: $color-black-primary;
  border: 1px solid $color-black-primary;
  color: $color-white-primary;
  display: flex;
  gap: 10px;
  padding: 6px 10px 4px;
  text-decoration: none;
  transition: 250ms ease-in-out;

  &:hover,
  &:focus {
    background-color: $color-white-primary;
    color: $color-black-primary;
  }
}

.NoLink {
  &:hover,
  &:focus {
    color: $color-black-primary;
  }
}

.Title {
  color: var(--color-theme--secondary);
  //margin-bottom: spacing(3);
  width: 100%;

  @include mq($breakpoint-desktop) {
    //margin-bottom: spacing(1);
  }
}

.Name {
  display: inline-block;
  line-height: 1;
}

.Icon {
  display: inline-block;
  margin-bottom: 3px;
}
