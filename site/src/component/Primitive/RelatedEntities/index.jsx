import React from 'react'
import { object } from 'prop-types'

import Entity from './component/Entity'
import EntitiesContainer from './component/EntitiesContainer'

const RelatedEntities = ({ article }) => {
  const { companies, executives } = article

  return (
    <>
      {companies && companies.length > 0 && (
        <EntitiesContainer title="Visit our company portals" inArticleBody>
          {companies.map((company, i) => (
            <Entity key={i} to="/company" as={`/company/${company.slug}`}>
              {company.name}
            </Entity>
          ))}
        </EntitiesContainer>
      )}
      {executives && executives.length > 0 && (
        <EntitiesContainer title="Executives">
          {executives.map((exec, j) => (
            <Entity key={j} to="/executive" as={`/executive/${exec.slug}`}>
              {exec.name}
            </Entity>
          ))}
        </EntitiesContainer>
      )}
    </>
  )
}

RelatedEntities.propTypes = {
  article: object
}

export default RelatedEntities
