.EventImageSplitHero {
  background-color: $color-white-primary;
  position: relative;

  .EventImageSplitHeroInner {
    @include mq($breakpoint-tablet) {
      display: flex;
      min-height: 350px;
    }

    @include mq($breakpoint-desktop) {
      min-height: 450px;
    }

    @include mq($breakpoint-desktopMedium) {
      min-height: 550px;
    }

    .ImagesWrapper {
      & > * {
        height: 100%;
      }

      @include mq($breakpoint-tablet) {
        flex: 1;
        margin-bottom: unset;
      }

      .ImageWrapper {
        position: relative;

        .Image {
          object-fit: cover;
          position: relative;
        }
      }
    }

    .ContentWrapper {
      padding-bottom: spacing(8);
      padding-top: spacing(8);

      @include mq($breakpoint-tablet) {
        align-items: center;
        display: flex;
        flex: 1.5;
        justify-content: center;
        text-align: left;
      }

      @include mq($breakpoint-desktop) {
        flex: 1;
      }

      .Content {
        padding-left: spacing(2);
        padding-right: spacing(2);

        @include mq($breakpoint-tablet) {
          padding-left: spacing(4);
          padding-right: spacing(4);
        }

        @include mq($breakpoint-desktopMedium) {
          max-width: spacing(80);
          padding-left: spacing(6);
          padding-right: spacing(6);
        }
      }
    }

    &.inverse {
      .ImagesWrapper {
        @include mq($breakpoint-tablet) {
          order: 2;
        }
      }

      .ContentWrapper {
        @include mq($breakpoint-tablet) {
          order: 1;
        }
      }
    }
  }

  &.grey {
    background-color: $color-grey95;
  }

  &.dark {
    @include darkMode;

    background-color: $color-black-brand;
  }
}

.Title {
  font-weight: $font-weight-extrabold;
  max-width: spacing(60);

  @include mq($breakpoint-desktop) {
    max-width: unset;
  }
}

.Description {
  p {
    font-size: inherit !important;
  }

  li {
    &::marker {
      color: var(--color-theme--event);
    }
  }

  strong {
    color: var(--color-theme--event) !important;
  }
}

.EventImageSplitHero.cover {
  .Image {
    height: 100%;
    top: unset;
    transform: unset;
  }
}

.EventImageSplitHero.column {
  padding-top: 0;

  .EventImageSplitHeroInner {
    display: grid;
    gap: spacing(2);
  }

  .ContentWrapper {
    padding-bottom: 0;
    padding-top: 0;
  }

  .Content {
    padding-left: 0 !important;
    padding-right: 0 !important;

    @include mq($breakpoint-tablet) {
      padding-left: spacing(2);
      padding-right: spacing(2);
    }
  }

  .OverflowContent {
    order: 3;
  }

  .ImageWrapper {
    aspect-ratio: 4/3;
    background-color: rgba(0, 0, 0, 0.05);
    /* stylelint-disable-next-line property-no-unknown */
    margin-left: auto;
    margin-right: auto;
    max-width: 600px;

    .Image {
      aspect-ratio: 4/3;
      height: unset !important;
      left: 50% !important;
      object-fit: unset !important;
      /* stylelint-disable-next-line property-no-unknown */
      position: relative !important;
      top: unset !important;
      transform: translateX(-50%) !important;
      width: 100%;
    }
  }

  @include mq($breakpoint-tablet) {
    padding-top: spacing(4);

    .Description {
      order: 3;
      text-align: center;

      li {
        list-style-position: inside;
      }
    }

    .ButtonGroup {
      justify-content: center;
    }
  }
}
