.Grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(120px, 100%), 1fr));
  list-style-type: none;
  overflow: hidden;
  position: relative;
}

.GridItem {
  padding: 12px;
}

.ImageHolder {
  aspect-ratio: 2/1;

  &::before,
  &::after {
    background-color: currentColor;
    content: '';
    display: block;
    position: absolute;
  }

  &::before {
    height: 100cqh;
    top: 0;
    translate: -13px 0;
    width: 1px;
  }

  &::after {
    height: 1px;
    left: 0;
    translate: 0 12px;
    width: 100cqw;
  }

  img {
    display: block;
    height: 100%;
    object-fit: contain;
    width: 100%;
  }
}
