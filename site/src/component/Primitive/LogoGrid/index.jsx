import React from 'react'
import ContainerQuery from '@/component/Primitive/ContainerQuery'
import styles from './LogoGrid.module.scss'
import { array } from 'prop-types'

const LogoGrid = ({ images }) => {
  return (
    <ContainerQuery containerName="LogoGrid">
      <ul className={styles.Grid}>
        {images.map((image, index) => (
          <li key={index} className={styles.GridItem}>
            <div className={styles.ImageHolder}>
              <img
                src={image.url}
                alt={image.caption || image.alt}
                width={image.width}
                height={image.height}
                loading="lazy"
                decoding="async"
              />
            </div>
          </li>
        ))}
      </ul>
    </ContainerQuery>
  )
}

LogoGrid.propTypes = {
  images: array.isRequired
}

export default LogoGrid
