import React from 'react'
import { string, node } from 'prop-types'

const ContainerQuery = ({ containerName, children, className = null }) => (
  <div
    style={{ 'container-name': containerName, 'container-type': 'inline-size' }}
    className={className}
  >
    {children}
  </div>
)

ContainerQuery.propTypes = {
  containerName: string.isRequired,
  children: node,
  className: string
}

export default ContainerQuery
