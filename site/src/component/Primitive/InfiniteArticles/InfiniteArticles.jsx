import React, { useEffect, useRef, useState, useCallback } from 'react'
import Article from '@/component/Primitive/InfiniteArticles/Article'
import NextArticleCTA from '@/component/Primitive/InfiniteArticles/NextArticleCTA'
import { object } from 'prop-types'
import createQuery from '@/query/article/articles-related-to-article'

const InfiniteArticles = (props) => {
  const { instance, latestMagazineIssue, pageData } = props
  const originalData = props.article
  originalData.url = pageData.url

  const loaderRef = useRef(null)
  const [articles, setArticles] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [page, setPage] = useState(0)
  const [currentArticleIds, setCurrentArticleIds] = useState({
    [originalData._id]: true
  })
  const [hasMore, setHasMore] = useState(true)
  const [articleCache, setArticleCache] = useState([])
  const [ctaArticle, setCtaArticle] = useState(null)

  const fetchArticleBatch = useCallback(
    async (pageNum) => {
      setIsLoading(true)
      try {
        const url = window.location.origin + '/graphql'
        const vars = {
          id: originalData._id,
          page: pageNum,
          url: window.location.href
        }
        const query = createQuery()
        const response = await fetch(url, {
          headers: { 'content-type': 'application/json' },
          method: 'POST',
          body: JSON.stringify({ query, variables: vars })
        })

        const json = await response.json()
        const results = json?.data?.articlesRelatedToArticle?.results || []

        if (results.length === 0) {
          setHasMore(false)
          return []
        }

        const filteredResults = results.filter(
          (article) => !currentArticleIds[article._id]
        )

        const newIds = {}
        filteredResults.forEach((article) => {
          newIds[article._id] = true
        })

        setCurrentArticleIds((prevState) => ({
          ...prevState,
          ...newIds
        }))

        return filteredResults
      } catch (error) {
        console.error('Error fetching more articles:', error)
        return []
      } finally {
        setIsLoading(false)
      }
    },
    [currentArticleIds, originalData._id]
  )

  useEffect(() => {
    if (articleCache.length === 0 && hasMore && !isLoading) {
      const prefetchNextBatch = async () => {
        const nextPage = page + 1
        const newArticles = await fetchArticleBatch(nextPage)

        if (newArticles.length > 0) {
          setArticleCache(newArticles)
          setPage(nextPage)
        }
      }

      prefetchNextBatch()
    }
  }, [articleCache, hasMore, fetchArticleBatch, page, isLoading])

  const loadNextArticle = useCallback(async () => {
    if (isLoading || (!hasMore && articleCache.length === 0)) return

    if (articleCache.length > 0) {
      const nextArticleToShow = articleCache[0]

      setArticleCache((prevCache) => prevCache.slice(1))
      setArticles((prev) => [...prev, nextArticleToShow])
    } else {
      const nextPage = page + 1
      const newArticles = await fetchArticleBatch(nextPage)

      if (newArticles.length > 0) {
        setArticles((prev) => [...prev, newArticles[0]])

        if (newArticles.length > 1) {
          setArticleCache(newArticles.slice(1))
        }

        setPage(nextPage)
      }
    }
  }, [articleCache, fetchArticleBatch, hasMore, isLoading, page])

  useEffect(() => {
    if (!hasMore && articleCache.length === 0) return

    const observerOptions = {
      root: null,
      rootMargin: '2000px',
      threshold: 0.1
    }

    const current = loaderRef.current
    const observer = new IntersectionObserver((entries) => {
      const entry = entries.find((e) => e.target === loaderRef.current)
      if (entry && entry.isIntersecting && !isLoading) {
        loadNextArticle()
      }
    }, observerOptions)

    if (current) {
      observer.observe(current)
    }

    return () => {
      if (current) {
        observer.unobserve(current)
      }
    }
  }, [
    articles,
    isLoading,
    hasMore,
    loadNextArticle,
    articleCache.length,
    loaderRef
  ])

  return (
    <>
      <NextArticleCTA nextArticle={ctaArticle} />

      {articles.map((article) => (
        <div key={article._id} style={{ paddingTop: '64px' }}>
          <Article
            originalData={originalData}
            instance={instance}
            latestMagazineIssue={latestMagazineIssue}
            pageData={pageData}
            article={article}
            setArticleCTA={(article) => setCtaArticle(article)}
          />
        </div>
      ))}

      {(hasMore || articleCache.length > 0) && <div ref={loaderRef} />}
    </>
  )
}

InfiniteArticles.propTypes = {
  instance: object,
  latestMagazineIssue: object,
  pageData: object,
  article: object
}

export default InfiniteArticles
