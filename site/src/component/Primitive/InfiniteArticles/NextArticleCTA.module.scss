.NextArticleCTA {
  animation: slideIn 500ms ease-in forwards;
  background-color: $color-black-primary;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  color: $color-white-primary;
  font-size: 12px;
  inset: auto auto 20px 20px;
  line-height: 1.25;
  max-width: 320px;
  padding: 16px;
  position: fixed;
  z-index: 500;

  &:hover {
    .NextArticleCTAGoTo {
      color: var(--color-theme--secondary);
    }
  }
}

@media (max-width: 639px) {
  .NextArticleCTA {
    display: none;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(200%);
  }
  to {
    transform: translateY(0);
  }
}

.NextArticleCTAInner {
  align-items: start;
  display: grid;
  gap: 1em;
  grid-template-columns: 80px 1fr;
}

.NextArticleCTATitle {
  cursor: pointer;
  margin: unset;
  padding: unset;
  text-align: left;

  &::before {
    content: '';
    inset: 0;
    position: absolute;
    z-index: 2;
  }
}

.NextArticleCTAHeading {
  color: var(--color-theme--secondary);
  font-weight: 700;
}

.NextArticleCTAGoTo {
  align-items: center;
  display: inline-flex;
  font-weight: 700;
  gap: 0.5em;
  margin-top: 1em;
}

.ArrowIcon {
  flex-shrink: 0;
}
