import React from 'react'
import styles from './NextArticleCTA.module.scss'
import cx from 'classnames'
import Icon from '@/component/Primitive/Icon'
import { object } from 'prop-types'

const NextArticleCTA = ({ nextArticle }) => {
  if (!nextArticle) {
    return null
  }

  const { headline, images } = nextArticle
  const thumbnailUrl = images?.thumbnail_landscape_322?.[0]?.url || false

  const handleClick = () => {
    const { top } = nextArticle?.ref?.current?.getBoundingClientRect()

    const y =
      top + window.scrollY - document.querySelector('header').offsetHeight + 22

    window.scrollTo({ top: y, behavior: 'smooth' })
  }

  return (
    <div className={cx(styles.NextArticleCTA)}>
      <div className={cx(styles.NextArticleCTAInner)}>
        <div className="next-article-image">
          {thumbnailUrl && <img src={thumbnailUrl} alt="" />}
        </div>

        <div>
          <button
            type="button"
            onClick={handleClick}
            className={cx(styles.NextArticleCTATitle)}
          >
            <span className={cx(styles.NextArticleCTAHeading)}>Up Next:</span>{' '}
            {headline}
          </button>

          <span className={cx(styles.NextArticleCTAGoTo)}>
            Jump to article{' '}
            <Icon
              className={cx(styles.ArrowIcon)}
              type="arrow-down"
              width={10}
              height={6}
            />
          </span>
        </div>
      </div>
    </div>
  )
}

NextArticleCTA.propTypes = {
  nextArticle: object
}

export default NextArticleCTA
