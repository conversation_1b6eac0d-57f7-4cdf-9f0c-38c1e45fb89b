import React, { useEffect, useRef, useState } from 'react'
import Row from '@/component/Structure/Row'
import FixedColumnRowLayout from '@/component/Structure/Layout/component/FixedColumnRowLayout'
import StandardRowLayout from '@/component/Structure/Layout/component/StandardRowLayout'
import Stack from '@/component/Primitive/Stack'
import useReadingTime from '@/hook/useReadingTime'
import { object, func } from 'prop-types'

const Article = (props) => {
  const {
    instance,
    latestMagazineIssue,
    pageData,
    article,
    setArticleCTA
  } = props
  const section = article.section
  const [articleRef, stats] = useReadingTime()
  const [hasViewed, setHasViewed] = useState(false)
  const url = new URL(article.fullUrlPath, window.location.href)?.toString()
  const inViewRef = useRef(null)

  useEffect(() => {
    const current = inViewRef.current
    if (!current) {
      return
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          window.history.replaceState(
            { source: 'intersection-observer' },
            '',
            url
          )

          document.title = `${article.headline} | ${instance.name}`

          if (!hasViewed) {
            if (typeof window?.cX?.sendPageViewEvent === 'function') {
              window.cX.sendPageViewEvent({ location: url })
            }

            if (typeof window.gtag === 'function') {
              window.gtag('event', 'page_view', {
                page_title: article.headline,
                page_location: url
              })
            }

            setHasViewed(true)
          }
        }

        setArticleCTA(null)
      },
      {
        root: null,
        rootMargin: '10%',
        threshold: 0.1
      }
    )

    const ctaObserver = new IntersectionObserver(
      (entries) => {
        const [entry] = entries

        if (entry.isIntersecting) {
          setArticleCTA({ ...article, ref: inViewRef })
        }
      },
      {
        root: null,
        rootMargin: '0px 0px 3000px 0px',
        threshold: 0.1
      }
    )

    observer.observe(current)
    ctaObserver.observe(current)

    return () => {
      observer.unobserve(current)
      ctaObserver.unobserve(current)
    }
    // eslint-disable-next-line
  }, [inViewRef, hasViewed])

  pageData.url = url
  article.readingTime = stats && Math.round(stats.minutes)
  const layout =
    section &&
    section.layouts &&
    section.layouts.article &&
    section.layouts.article.layout

  if (layout.length) {
    return (
      <div ref={inViewRef}>
        <div ref={articleRef}>
          <Stack>
            {layout.map((row, rowIndex) => {
              const rowLayoutProps = {
                article,
                section,
                instance,
                pageData,
                row,
                latestMagazineIssue,
                rowIndex
              }
              const rowHasAFixedColumn =
                row.attributes && row.attributes.includes('fixed-column')

              if (rowHasAFixedColumn) {
                return (
                  <Row
                    flex={rowHasAFixedColumn}
                    key={rowIndex}
                    row={row}
                    isArticle={!!article}
                  >
                    <FixedColumnRowLayout
                      isArticle={!!article}
                      {...rowLayoutProps}
                    />
                  </Row>
                )
              }

              return (
                <Row key={rowIndex} row={row} isArticle={!!article}>
                  <StandardRowLayout {...rowLayoutProps} />
                </Row>
              )
            })}
          </Stack>
        </div>
      </div>
    )
  }

  return <></>
}

Article.propTypes = {
  pageData: object,
  article: object,
  instance: object,
  latestMagazineIssue: object,
  originalData: object,
  setArticleCTA: func
}

export default Article
