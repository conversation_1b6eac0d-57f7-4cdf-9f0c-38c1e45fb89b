import classNames from 'classnames'
import { func, string } from 'prop-types'
import React, { useEffect, useRef, useState } from 'react'
import Icon from '../Icon'
import SmartLink from '../SmartLink'
import IconButton from '../IconButton'
import styles from './StoryCTA.module.scss'
import Tilt from '@/component/Primitive/Tilt'

const StoryCTA = ({ imageUrl, title, to, as }) => {
  const storageKey = `dismissed_${to}`
  const [visible, setVisible] = useState(false)
  const inViewRef = useRef(null)

  useEffect(() => {
    const isServer = typeof window === 'undefined'
    if (isServer) return

    const dismissed = !!window.localStorage.getItem(storageKey)
    setVisible(!dismissed)
  }, [storageKey])

  useEffect(() => {
    if (!inViewRef.current) return

    const grandparent = inViewRef.current.parentElement?.parentElement
    if (!grandparent) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (inViewRef.current) {
          inViewRef.current.style.display = entry.isIntersecting ? null : 'none'
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
      }
    )

    observer.observe(grandparent)

    return () => {
      observer.unobserve(grandparent)
    }
  }, [visible])

  const handleDismiss = () => {
    window.localStorage.setItem(storageKey, 'true')
    setVisible(false)
  }

  return (
    visible && (
      <div ref={inViewRef}>
        <StoryCTAComponent
          imageUrl={imageUrl}
          title={title}
          to={to}
          as={as}
          onDismiss={handleDismiss}
        />
      </div>
    )
  )
}

export default StoryCTA

StoryCTA.propTypes = {
  imageUrl: string.isRequired,
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired
}

const StoryCTAComponent = ({ imageUrl, title, to, as, onDismiss }) => {
  return (
    <div className={classNames(styles.StoryCTA)}>
      <div className={styles.StoryCTAInner}>
        <SmartLink to={to} as={as} className={styles.StoryCTALink}>
          <Tilt>
            <img loading="lazy" src={imageUrl} alt="" />
          </Tilt>

          <div className={styles.StoryCTALinkText}>
            {title}

            <Icon className={styles.ArrowIcon} type="arrow-right" />
          </div>
        </SmartLink>

        {onDismiss && (
          <IconButton
            small
            className={styles.Dismiss}
            onClick={onDismiss}
            icon="close"
            a11yText="Dismiss"
            width={37}
            height={37}
          />
        )}
      </div>
    </div>
  )
}

StoryCTAComponent.propTypes = {
  imageUrl: string.isRequired,
  title: string.isRequired,
  to: string.isRequired,
  as: string.isRequired,
  onDismiss: func
}

export const StoryCTAContainer = () => (
  <div id="storyCTA" className={styles.StoryCTAContainer} />
)
