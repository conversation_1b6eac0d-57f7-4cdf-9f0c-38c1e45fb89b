.StoryCTA {
  align-items: end;
  display: flex;
  inset: auto auto 0 0;
  justify-content: center;
  max-width: 240px;
  min-width: 160px;
  position: fixed;
  width: 16vw;
  z-index: 5;

  &::before {
    aspect-ratio: 1; //stylelint-disable-line
    background: radial-gradient(
      circle at center,
      rgba(0, 0, 0, 1) 0%,
      rgba(0, 0, 0, 0) 60%
    );
    content: '';
    display: block;
    height: 250%;
    inset: auto auto 0 0;
    position: absolute;
    transform: translate(-50%, 50%);
    transition: 250ms ease-in-out;
  }

  &:has(a:active, a:hover) {
    &::before {
      transform: translate(-48%, 48%);
    }
  }
}

@media (min-width: 640px) {
  .StoryCTA {
    body:has([class^='NextArticleCTA']) & {
      display: none;
    }
  }
}

.StoryCTAInner {
  max-width: 180px;
  min-width: 120px;
  padding-bottom: 24px;
  position: relative;
  width: 12vw;
  z-index: 5;

  img {
    width: 100%;
  }
}

.StoryCTALink {
  display: grid;
  gap: 16px;

  &:link,
  &:visited {
    text-decoration: none;
  }
}

.StoryCTALinkText {
  align-items: center;
  color: $color-white-primary;
  display: flex;
  font-size: clamp(0.75rem, calc(0rem + 1.5625vw), 1rem);
  gap: 0.5em;
  justify-content: center;
  text-align: center;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);

  svg {
    display: block;
    height: 0.5em !important;
    width: auto !important;
  }
}

.ArrowIcon {
  flex-shrink: 0;
  height: unset !important;
  line-height: unset !important;
  width: unset !important;
}

.Dismiss {
  background: $color-black-primary;
  color: $color-white-primary;
  display: flex;
  height: 24px;
  inset: -32px -32px auto auto;
  justify-content: center;
  min-width: unset;
  position: absolute;
  width: 24px;

  svg {
    display: block;
  }
}
