import React from 'react'
import { string } from 'prop-types'

import ArticleHeader1 from './component/ArticleHeader1'
import ArticleHeader2 from './component/ArticleHeader2'
import ArticleHeader3 from './component/ArticleHeader3'

const typeMap = (props) => ({
  Interview: <ArticleHeader2 {...props} />,
  'Company Report': <ArticleHeader2 {...props} />,
  Whitepaper: <ArticleHeader2 {...props} hideShortHeadline />,
  Event: <ArticleHeader3 {...props} />
})

const ArticleHeader = (props) => {
  const { contentType, category } = props
  return (
    typeMap(props)[contentType] || (
      <ArticleHeader1
        breadcrumbs={[{ name: contentType }, { name: category }]}
        {...props}
        hideOnDesktop
      />
    )
  )
}

ArticleHeader.propTypes = {
  contentType: string.isRequired,
  category: string
}

export default ArticleHeader
