import React from 'react'
import { format } from 'date-fns'
import { string, arrayOf, shape, number, bool } from 'prop-types'

import styles from './ArticleHeader.module.scss'
import Breadcrumbs from '../../Breadcrumbs'
import Type from '../../Type'
import SmartLink from '../../SmartLink'
import ArticleShare from '../../ArticleShare'

const ArticleHeader1 = ({
  headline,
  breadcrumbs,
  author,
  displayDate,
  readingTime,
  __shareUrl,
  hideOnDesktop
}) => {
  const formattedReadingTime = `${readingTime} ${
    readingTime === 1 ? 'min' : 'mins'
  }`
  const formattedDisplayDate = format(new Date(displayDate), 'MMMM dd, yyyy')
  return (
    <div className={styles.ArticleHeader1}>
      <Breadcrumbs breadcrumbs={breadcrumbs} />
      <div className={styles.Headline}>
        <Type themed as="h1" size={['heading3', 'display1']} weight="bold">
          {headline}
        </Type>
      </div>
      <div className={styles.Details}>
        <div className={styles.DetailsContent}>
          {author && author?.slug && (
            <Type size={['body3', 'body3']} weight="medium">
              By{' '}
              <SmartLink
                to="/author"
                as={`/author/${author?.slug}`}
                className={styles.Author}
              >
                {author?.name}
              </SmartLink>
            </Type>
          )}
          {author && !author?.slug && (
            <Type size={['body3', 'body3']} weight="medium">
              By {author?.name}
            </Type>
          )}
          <Breadcrumbs
            highlight="none"
            breadcrumbs={[formattedDisplayDate, formattedReadingTime].map(
              (item) => ({
                name: item
              })
            )}
          />
        </div>
        <ArticleShare url={__shareUrl} hideOnDesktop={hideOnDesktop} />
      </div>
    </div>
  )
}

ArticleHeader1.propTypes = {
  headline: string.isRequired,
  breadcrumbs: arrayOf(
    shape({
      name: string.isRequired
    })
  ).isRequired,
  author: shape({
    name: string.isRequired
  }),
  displayDate: string.isRequired,
  readingTime: number,
  __shareUrl: string,
  hideOnDesktop: bool
}

export default ArticleHeader1
