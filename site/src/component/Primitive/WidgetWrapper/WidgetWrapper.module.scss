.WidgetWrapper {
  height: 100%;
  position: relative;

  &:empty {
    margin-top: 0;
  }

  &:first-child:empty {
    margin-bottom: spacing(-4);
  }
}

@include mq($max: $breakpoint-tablet - 1) {
  .hidden-mobile {
    display: none;
  }
}

@include mq($breakpoint-tablet, $max: $breakpoint-desktop - 1) {
  .hidden-tablet {
    display: none;
  }
}

@include mq($breakpoint-desktop) {
  .hidden-desktop {
    display: none;
  }
}

.sticky {
  height: auto;
  margin-top: spacing(3);

  &:first-child {
    margin-top: 0 !important;
  }
}

@media (min-width: #{$breakpoint-desktop}) and (min-height: 750px) {
  .sticky {
    position: sticky;
    top: 108px;
  }
}

.Anchor {
  position: absolute;
  top: -100px;
  @include mq($breakpoint-desktop) {
    top: -210px;
  }
}
