import React from 'react'
import { arrayOf, object } from 'prop-types'

import styles from './ArticleTags.module.scss'
import Tag from '../Tag'
import Type from '@/component/Primitive/Type'

const ArticleTags = ({ tags }) => {
  if (!tags || tags.length === 0) return null
  return (
    <div className={styles.ArticleTags}>
      <Type
        size={['body2', 'body3']}
        weight="medium"
        uppercase
        className={styles.Title}
        as="h2"
      >
        Tags
      </Type>

      {tags.map((tag) => (
        <Tag
          href={`/search?q=${encodeURIComponent(tag.tag)}`}
          className={styles.Tag}
          background="black"
          uppercase
          nofollow
          key={tag.tag}
        >
          {tag.tag}
        </Tag>
      ))}
    </div>
  )
}

ArticleTags.propTypes = {
  tags: arrayOf(object)
}

export default ArticleTags
