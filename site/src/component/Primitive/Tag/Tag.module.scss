.Tag {
  background-color: $color-white-primary;
  border: 1px solid $color-misc-divider;
  color: $color-black-secondary;
  display: inline-block;
  margin-right: spacing(0.5);
  padding: 0 6px;
  text-decoration: none;
  transition: 250ms ease-in-out;

  @include mq($breakpoint-desktop) {
    line-height: 1;
  }
}

a.Tag:hover,
a.Tag:focus {
  background-color: $color-grey-secondary;
}

.transparent {
  background-color: transparent;
}

.white {
  background-color: $color-white-primary;
}

.black {
  background-color: $color-black-primary;
  border: 1px solid $color-black-primary;
  color: $color-white-primary;

  &:hover,
  &:focus {
    background-color: $color-white-primary !important;
    color: $color-black-primary !important;
  }
}

.grey {
  background-color: $color-grey-primary;
  border: 0;
  color: $color-white-primary;
}

.uppercase {
  text-transform: uppercase;
}

.themed {
  color: var(--color-theme--secondary);
}
