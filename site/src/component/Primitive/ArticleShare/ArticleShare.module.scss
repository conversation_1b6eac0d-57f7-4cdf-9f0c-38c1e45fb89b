.ArticleShare {
  padding-bottom: spacing(2);

  @include mq($breakpoint-desktop) {
    padding-bottom: spacing(0);
  }
}

@media (min-width: 1332px) {
  .ArticleShareNoDesktop {
    display: none !important;
  }
}

.Title {
  color: $color-misc-red;
  margin-bottom: spacing(1);
  text-transform: uppercase;

  @include mq($breakpoint-desktop) {
    margin-bottom: spacing(0.5);
  }
}

.Share {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.ShareItem {
  color: $color-black-primary;
  display: block;

  svg {
    fill: $color-black-primary;
    height: auto;
    width: 20px;
  }

  &:hover,
  &:focus {
    svg,
    path {
      fill: var(--color-theme--secondary);
    }
  }
}
