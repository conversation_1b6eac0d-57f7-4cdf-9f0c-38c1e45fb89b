import React from 'react'
import { bool, string } from 'prop-types'
import cx from 'classnames'

import {
  EmailShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton
} from 'react-share'

import styles from './ArticleShare.module.scss'
import stickyStyles from './StickyArticleShare.module.scss'

import Icon from '../Icon'
import Type from '../Type'

const ArticleShare = ({ url, sticky, hideOnDesktop }) => (
  <div
    className={cx(
      styles.ArticleShare,
      hideOnDesktop && styles.ArticleShareNoDesktop,
      sticky && stickyStyles.StickyArticleShare
    )}
  >
    <Type
      size={['body2', 'body4']}
      weight="medium"
      className={cx(
        styles.Title,
        sticky && stickyStyles.StickyArticleShareTitle
      )}
    >
      Share
    </Type>

    <div
      className={cx(styles.Share, sticky && stickyStyles.StickyArticleShareRow)}
    >
      <LinkedinShareButton
        className={cx(
          styles.ShareItem,
          sticky && stickyStyles.StickyArticleShareItem
        )}
        url={url}
      >
        <Icon
          width="15"
          type="linkedin"
          className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
        />
      </LinkedinShareButton>

      <TwitterShareButton
        className={cx(
          styles.ShareItem,
          sticky && stickyStyles.StickyArticleShareItem
        )}
        url={url}
      >
        <Icon
          width="15"
          type="twitter"
          className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
        />
      </TwitterShareButton>

      <FacebookShareButton
        className={cx(
          styles.ShareItem,
          sticky && stickyStyles.StickyArticleShareItem
        )}
        url={url}
      >
        <Icon
          width="18"
          type="facebook"
          className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
        />
      </FacebookShareButton>

      <EmailShareButton
        subject="I wanted you to see this article"
        body="Check out this article"
        className={cx(
          styles.ShareItem,
          sticky && stickyStyles.StickyArticleShareItem
        )}
        url={url}
      >
        <Icon
          width="18"
          type="email"
          className={cx(sticky && stickyStyles.StickyArticleShareIcon)}
        />
      </EmailShareButton>
    </div>
  </div>
)

ArticleShare.propTypes = {
  url: string.isRequired,
  sticky: bool,
  hideOnDesktop: bool
}

export default ArticleShare
