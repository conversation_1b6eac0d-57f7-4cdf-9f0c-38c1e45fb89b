.StickyArticleShareColumn {
  width: 0;
}

.StickyArticleShare {
  background-color: var(--color-theme--primary);
  border-radius: 48px;
  padding-block: 6px;
  position: sticky;
  top: 108px;
  transform: translateX(-100px);
  width: 48px;

  > div {
    align-items: center;
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 1331px) {
  .StickyArticleShare {
    display: none;
  }
}

.StickyArticleShareTitle {
  inset: auto auto 8px -6px;
  position: absolute;
  transform: rotate(-90deg);
  transform-origin: left bottom;
}

.StickyArticleShareRow {
  align-items: center;
  flex-direction: column;
  gap: 12px;
}

.StickyArticleShareItem {
  border: 1px solid $color-white-primary !important;
  border-radius: 36px;
  color: $color-white-primary !important;
  display: grid;
  height: 36px;
  place-content: center;
  transition: 250ms ease-in-out;
  width: 36px;

  svg {
    &,
    path {
      fill: currentColor !important;
    }
  }

  &:active,
  &:hover {
    background-color: $color-white-primary !important;
    color: var(--color-theme--primary) !important;
  }

  span {
    display: block;
    height: auto !important;
    width: 20px !important;
  }
}
