import React, { useContext } from 'react'
import cx from 'classnames'
import { array, string } from 'prop-types'

import List from '@/component/Primitive/List'
import SmartLink from '@/component/Primitive/SmartLink'
import Type from '@/component/Primitive/Type'
import Tag from '@/component/Primitive/Tag'
import Inline from '@/component/Primitive/Inline'

import styles from './SimpleList.module.scss'
import { RowContext } from '@/component/Context/RowContext'

const SimpleList = ({ listData, titlePrimary, titleSecondary, className }) => {
  const inverse = useContext(RowContext)
  return (
    <List unstyled className={cx(styles.SimpleList, className)}>
      {titlePrimary && (
        <div className={styles.SimpleListTitle}>
          <Type size="heading8" weight="bold">
            {titlePrimary} {titleSecondary && titleSecondary}
          </Type>
        </div>
      )}
      {listData.map((item, i) => {
        if (!item) return
        return (
          <li key={`simplelist-item-${i}`}>
            <SmartLink
              to={item.eventBaseSlug ? '/eventArticle' : '/article'}
              as={item.href}
              className={cx(styles.SimpleListLink, inverse && styles.inverse)}
            >
              <Type themed size="heading6" weight="bold">
                {item.content}
              </Type>
              {item.tags && (
                <div className={styles.SimpleListTags}>
                  <Inline gap="small">
                    {item.tags.map((tag, i) => (
                      <Tag key={i}>{tag.title}</Tag>
                    ))}
                  </Inline>
                </div>
              )}
            </SmartLink>
          </li>
        )
      })}
    </List>
  )
}

SimpleList.propTypes = {
  listData: array.isRequired,
  titlePrimary: string,
  titleSecondary: string,
  className: string
}

export default SimpleList
