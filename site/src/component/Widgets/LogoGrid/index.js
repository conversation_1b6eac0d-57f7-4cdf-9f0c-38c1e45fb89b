import React from 'react'
import { array } from 'prop-types'
import LogoGrid from '@/component/Primitive/LogoGrid'

const LogoGridWidget = ({ logoGridImages }) => {
  const arr = []

  for (const { images } of logoGridImages) {
    const i = images?.logo_free_640

    for (const j of i) {
      if ('url' in j) {
        arr.push(j)
      }
    }
  }

  arr.push(...arr)
  arr.push(...arr)
  arr.push(...arr)

  return <LogoGrid images={arr} />
}

LogoGridWidget.propTypes = {
  logoGridImages: array
}

export default LogoGridWidget
