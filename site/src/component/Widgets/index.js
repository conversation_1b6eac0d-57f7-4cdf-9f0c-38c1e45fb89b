const widgets = {
  imageGrid: require('./ImageGrid').default,
  articleGrid: require('./ArticleGrid').default,
  eventArticleGrid: require('./EventArticleGrid').default,
  eventArticleGridSnippet: require('./EventArticleGridSnippet').default,
  simpleArticleGrid: require('./SimpleArticleGrid').default,
  articleStack: require('./ArticleStack').default,
  articleLayoutBody: require('./ArticleLayoutBody').default,
  articleLayoutHeader: require('./ArticleLayoutHeader').default,
  html: require('./Html').default,
  inlineImage: require('./InlineImage').default,
  inlineVideo: require('./InlineVideo').default,
  articleLayoutShare: require('./ArticleLayoutShare').default,
  blockquote: require('./Blockquote').default,
  text: require('./Text').default,
  editorialIntro: require('./EditorialIntro').default,
  editorialContactForm: require('./EditorialContactForm').default,
  eventStats: require('./EventStats').default,
  eventMap: require('./EventMap').default,
  eventDetails: require('./EventDetails').default,
  eventMagazineRow: require('./EventMagazineRow').default,
  // eventForm: require('./EventForm').default,
  advert: require('./Advert').default,
  videoGrid: require('./VideoGrid').default,
  carousel: require('./Carousel').default,
  aboutBrand: require('./AboutBrand').default,
  advertiseInfo: require('./AdvertiseInfo').default,
  advertiseTestimonial: require('./AdvertiseTestimonial').default,
  partners: require('./Partners').default,
  podcast: require('./Podcast').default,
  newsletterSignup: require('./NewsletterSignup').default,
  mostPopularOrViewed: require('./MostPopularOrViewed').default,
  keyFacts: require('./KeyFacts').default,
  tweet: require('./Tweet').default,
  advertiseHero: require('./AdvertiseHero').default,
  header: require('./Header').default,
  magazineIssueViewer: require('./MagazineIssueViewer').default,
  companyProfile: require('./CompanyProfile').default,
  companyArticles: require('./CompanyArticles').default,
  executivesInCompany: require('./ExecutivesInCompany').default,
  executiveProfile: require('./ExecutiveProfile').default,
  executiveArticles: require('./ExecutiveArticles').default,
  authorProfile: require('./AuthorProfile').default,
  authorArticles: require('./AuthorArticles').default,
  latestMagazineIssue: require('./LatestMagazineIssue').default,
  featuredMagazine: require('./FeaturedMagazine').default,
  magazineGrid: require('./MagazineGrid').default,
  pastMagazineIssues: require('./PastMagazineIssues').default,
  paginatedList: require('./PaginatedList').default,
  gridWithAdvert: require('./GridWithAdvert').default,
  articleLayoutTags: require('./ArticleLayoutTags').default,
  magazineCta: require('./MagazineCta').default,
  articleLayoutStoryCta: require('./ArticleLayoutStoryCta').default,
  articleLayoutEventInfo: require('./ArticleLayoutEventInfo').default,
  articleLayoutDownload: require('./ArticleLayoutDownload').default,
  articleLayoutStandfirst: require('./ArticleLayoutStandfirst').default,
  articleLayoutImages: require('./ArticleLayoutImages').default,
  articleLayoutRelatedContent: require('./ArticleLayoutRelatedContent').default,
  articleLayoutRelatedEntities: require('./ArticleLayoutRelatedEntities')
    .default,
  articleLayoutEventRegistration: require('./ArticleLayoutEventRegistration')
    .default,
  brandStats: require('./BrandStats').default,
  aboutLiveEvents: require('./AboutLiveEvents').default,
  aboutBizClik: require('./AboutBizClik').default,
  aboutCta: require('./AboutCta').default,
  brandFeatures: require('./BrandFeatures').default,
  advertiseFeatures: require('./AdvertiseFeatures').default,
  brandLocation: require('./BrandLocation').default,
  advertiseSalesforceForm: require('./AdvertiseSalesforceForm').default,
  prnewswire: require('./Prnewswire').default,
  placeholder: require('./Placeholder').default,
  requestThankYou: require('./RequestThankYou').default,
  eventLivePlayer: require('./EventLivePlayer').default,
  eventOnDemandPlayer: require('./EventOnDemandPlayer').default,
  eventTestimonialGrid: require('./EventTestimonialGrid').default,
  eventContentGrid: require('./EventContentGrid').default,
  eventContentBlock: require('./EventContentBlock').default,
  eventTextBlock: require('./EventTextBlock').default,
  eventButtonGroup: require('./EventButtonGroup').default,
  eventTieredSponsor: require('./EventTieredSponsor').default,
  eventFeaturedTestimonial: require('./EventFeaturedTestimonial').default,
  eventHero: require('./EventHero').default,
  eventCountdown: require('./EventCountdown').default,
  eventArticleSplitHero: require('./EventArticleSplitHero').default,
  eventImageSplitHero: require('./EventImageSplitHero').default,
  eventVideoSplitHero: require('./EventVideoSplitHero').default,
  eventWOFSplitHero: require('./EventWOFSplitHero').default,
  eventAgendaSplitHero: require('./EventAgendaSplitHero').default,
  eventIconSplitHero: require('./EventIconSplitHero').default,
  eventSteps: require('./EventSteps').default,
  eventSponsorLayoutHeader: require('./EventSponsorLayoutHeader').default,
  eventSponsorLayoutBody: require('./EventSponsorLayoutBody').default,
  eventSponsorLayoutFooter: require('./EventSponsorLayoutFooter').default,
  genericCarousel: require('./GenericCarousel').default,
  magazineLatestCTA: require('./MagazineLatestCTA').default,
  eventSponsorStrip: require('./EventSponsorStrip').default,
  eventLatestCTA: require('./EventLatestCTA').default,
  eventAgendaNavigator: require('./EventAgendaNavigator').default,
  eventMapSplitHero: require('./EventMapSplitHero').default,
  eventSponsorHero: require('./EventSponsorHero').default,
  eventSponsorLinkedArticles: require('./EventSponsorLinkedArticles').default,
  eventGrid: require('./EventGrid').default,
  logoGrid: require('./LogoGrid').default,
  infiniteArticles: require('./InfiniteArticles').default
}

module.exports = widgets
