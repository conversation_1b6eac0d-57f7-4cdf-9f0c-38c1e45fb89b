/**
 * Helper hook to check if a feature flag is enabled for a given instance
 * @param {Object} instance - The instance object containing feature flags
 * @returns {Object} Object containing isFeatureEnabled method
 */
function useFeatureFlagHelper(instance) {
  /**
   * Checks if a specific feature flag is enabled
   * @param {string} flag - The feature flag to check
   * @returns {boolean} True if the feature is enabled, false otherwise
   */
  const isFeatureEnabled = (flag) => {
    if (!instance) {
      /* TODO - sentry log */ return false
    }
    if (!instance.featureFlags) {
      /* TODO - sentry log */ return false
    }
    return instance.featureFlags.includes(flag)
  }
  return { isFeatureEnabled }
}

export default useFeatureFlagHelper
