{"extends": ["stylelint-config-standard", "stylelint-config-css-modules", "stylelint-config-prettier"], "plugins": ["stylelint-scss"], "rules": {"at-rule-empty-line-before": null, "at-rule-no-unknown": null, "block-closing-brace-newline-after": ["always", {"ignoreAtRules": ["if", "else"]}], "block-opening-brace-space-before": "always", "no-descending-specificity": null, "property-no-unknown": [true, {"ignoreProperties": ["aspect-ratio", "container-name", "container-type", "container"]}], "scss/at-else-closing-brace-newline-after": "always-last-in-chain", "scss/at-else-closing-brace-space-after": "always-intermediate", "scss/at-else-empty-line-before": "never", "scss/at-if-closing-brace-newline-after": "always-last-in-chain", "scss/at-if-closing-brace-space-after": "always-intermediate", "scss/at-rule-no-unknown": [true, {"ignoreAtRules": ["container"]}], "unit-no-unknown": [true, {"ignoreUnits": ["cqw", "cqh", "cqi", "cqb", "cqmin", "cqmax"]}]}, "syntax": "scss"}