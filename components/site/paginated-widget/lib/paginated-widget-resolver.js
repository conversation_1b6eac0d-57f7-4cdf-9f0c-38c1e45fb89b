const createUrlEntitiesDeterminer = require('../../resource/lib/entity-determiner')
const createResourceDeterminer = require('../../resource/lib/resource-determiner')
const createDoorman = require('doorman')

const { GraphQLString, GraphQLNonNull, GraphQLScalarType } = require('graphql')

const GraphQLJSON = new GraphQLScalarType({
  name: 'JSON',
  description: 'Generic JSON scalar type',
  parseValue: (value) => value,
  serialize: (value) => value,
  parseLiteral: (ast) => ast.value
})

const resolvePaginatedWidget = (serviceLocator) => {
  const determineUrlEntities = createUrlEntitiesDeterminer(serviceLocator)
  const determineResource = createResourceDeterminer(serviceLocator)
  const args = {
    url: { type: GraphQLNonNull(GraphQLString) },
    widgetType: { type: GraphQLNonNull(GraphQLString) },
    currentArticleIds: { type: GraphQLJSON }
  }
  const resolve = async (source, args, context) => {
    const { account, instance, path } = await determineUrlEntities(args.url)
    const resource = await determineResource(account, instance, path)

    context.instanceId = instance._id
    context.accountId = account._id
    context.dedupe = createDoorman(args.currentArticleIds)

    if (
      args.widgetType === 'infiniteArticles' &&
      resource.section.layouts.article.layout
    ) {
      let widgetData = null
      resource.section.layouts.article.layout.some((l) => {
        return l.cols.some((r) => {
          return r.widgetArea.widgets.some((w) => {
            if (w.type === args.widgetType) {
              widgetData = w
              return true
            }
          })
        })
      })

      return widgetData
    }

    if (resource.article) {
      const widgetData = resource.article.body.widgets.find(
        (widget) => widget.type === args.widgetType
      )
      return widgetData
    } else if (resource.layouts.section.layout) {
      context.section = resource
      let widgetData = null
      resource.layouts.section.layout.some((l) => {
        return l.cols.some((r) => {
          return r.widgetArea.widgets.some((w) => {
            if (w.type === args.widgetType) {
              widgetData = w
              return true
            }
          })
        })
      })

      return widgetData
    }
    return null
  }
  return {
    args,
    resolve
  }
}

module.exports = resolvePaginatedWidget
