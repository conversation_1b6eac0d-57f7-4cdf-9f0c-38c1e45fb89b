const { promisify } = require('util')

const getInstanceFilteredServices = require('../../../service/section/lib/instance-filtered-service-retriever')
const createUrlEntitiesDeterminer = require('../../resource/lib/entity-determiner')
const createResourceDeterminer = require('../../resource/lib/resource-determiner')

const { GraphQLString, GraphQLNonNull, GraphQLInt } = require('graphql')

const resolver = (serviceLocator) => {
  const determineUrlEntities = createUrlEntitiesDeterminer(serviceLocator)
  const determineResource = createResourceDeterminer(serviceLocator)
  const args = {
    id: { type: GraphQLNonNull(GraphQLString) },
    page: { type: GraphQLInt },
    url: { type: GraphQLNonNull(GraphQLString) }
  }

  const resolve = async (source, args, context) => {
    const { account, instance, path } = await determineUrlEntities(args.url)
    const resource = await determineResource(account, instance, path)
    if (!resource) {
      return null
    }

    const { articleService } = await getInstanceFilteredServices(
      serviceLocator
    )(instance._id, account._id)

    const article = await promisify(serviceLocator.articleService.read)(args.id)
    const { _id, tags } = article

    if (!tags || !tags.length) {
      return null
    }

    const query = { _id: { $ne: _id } }
    const sanitisedTags = tags.map(({ tag }) => tag)

    if (sanitisedTags.length) {
      query['tags.tag'] = { $in: sanitisedTags }
    }

    if (context.region) {
      query.$or = [{ region: context.region }, { region: { $size: 0 } }]
    }

    const page = args.page || 1
    const numPerPage = 8
    const skip = Math.max((page - 1) * numPerPage, 0)
    const options = { limit: numPerPage, skip, sort: { displayDate: -1 } }
    const total = await promisify(articleService.countPublic)(query)
    const articles = await promisify(articleService.findPublic)(query, options)

    return { total, results: articles }
  }
  return {
    args,
    resolve
  }
}

module.exports = resolver
