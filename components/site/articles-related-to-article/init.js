const schemata = require('@clocklimited/schemata')
const convertSchemataToGraphQl = require('../../../api/lib/schemata-graphql-adaptor')
const createResolver = require('./lib/resolver')
const createArticleSchema = require('../../service/article/graphql-schema')

const init = (serviceLocator, done) => {
  const createSchema = () =>
    schemata({
      name: 'ArticlesRelatedToArticle',
      properties: {
        total: {
          type: Number
        },
        results: {
          type: schemata.Array(createArticleSchema(serviceLocator))
        }
      }
    })

  serviceLocator.graphqlFactory.query.set(
    'articlesRelatedToArticle',
    (types) => ({
      type: convertSchemataToGraphQl(createSchema(), types),
      ...createResolver(serviceLocator)
    })
  )

  done()
}

module.exports = () => ({
  articlesRelatedToArticleApi: ['graphqlApi', init]
})
