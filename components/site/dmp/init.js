const schemata = require('@clocklimited/schemata')
const createSiteGroupsResolver = require('./lib/site-groups-resolver')
const convertSchemataToGraphQl = require('../../../api/lib/schemata-graphql-adaptor')
const { default: createController } = require('./controllers/controller')

const init = (serviceLocator, done) => {
  createController(serviceLocator)

  const createDmpSiteGroupSchema = () =>
    schemata({
      name: 'DmpSiteGroups',
      properties: {
        results: {
          type: Array
        }
      }
    })

  serviceLocator.graphqlFactory.query.set('dmpSiteGroups', (types) => ({
    type: convertSchemataToGraphQl(createDmpSiteGroupSchema(), types),
    ...createSiteGroupsResolver(serviceLocator)
  }))

  done()
}

module.exports = () => ({
  dmpApi: ['graphqlApi', init]
})
