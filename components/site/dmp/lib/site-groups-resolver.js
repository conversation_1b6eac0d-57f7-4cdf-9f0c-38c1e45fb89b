const { promisify } = require('util')

const resolver = (serviceLocator) => {
  const args = {}
  const resolve = async (source, args, context) => {
    const instances = await promisify(serviceLocator.instanceService.find)({})
    const siteGroupIds = instances
      .map((instance) => instance.pianoDmpSiteGroupId)
      .filter((id) => id)
    return { results: siteGroupIds }
  }
  return {
    args,
    resolve
  }
}

module.exports = resolver
