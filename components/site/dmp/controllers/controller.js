import { promisify } from 'util'
import bodyParser from 'body-parser'

const validateRequestBody = (body) => {
  if (!body) {
    throw new Error('Request body is required')
  }
  // [{ <id>: <shortId> }, ...]
  if (!Array.isArray(body)) {
    throw new Error('Request body must be an array')
  }
  body.forEach((item) => {
    if (typeof item !== 'object') {
      throw new Error('Request body must be an array of objects')
    }
  })
}

const createController = (serviceLocator) => {
  const DMPUserSegmentController = async (req, res) => {
    try {
      validateRequestBody(req.body)
      const targeting = await promisify(
        serviceLocator.instanceService.resolveUserTargetingValues
      )(req.body)
      res.status(200).json({ results: targeting })
    } catch (error) {
      serviceLocator.logger.error(error)
      res.status(500).json({ error: 'Internal Server Error: ' + error })
    }
  }

  serviceLocator.router.post(
    '/api/resolve-dmp-user-segments',
    bodyParser.json(),
    DMPUserSegmentController
  )
}

export default createController
