.js-item.list-item.list-item-detail(data-id=id)
  .image-wrapper
    i.image.image-detail(style="background-image: url('" + data.previewImageUrl + "');")
  .list-item-header
    .list-item-leading-actions
      span.label.label--large.sort-handle.js-sort-handle
        i.icon.icon--hamburger Move
    .list-item-actions
      .btn-group
        block actions
          a.btn.btn--small.dropdown-toggle(data-toggle='dropdown')
            span.caret
          ul.dropdown-menu.pull-right
            li
              a.js-edit-item Edit
              a.js-remove-item Remove

    block title
      h2
        a.js-edit-item Open Image ##{id}
