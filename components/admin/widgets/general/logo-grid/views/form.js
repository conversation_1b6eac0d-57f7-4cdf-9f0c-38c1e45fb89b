const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../templates/form.jade'))
const BaseWidgetView = require('../../../../widget/views/form/base')
const debug = require('../../../../../../admin/source/js/lib/debug')(
  'carousel widget form view'
)
const { crops } = require('../image-config.json')
const ItemRepeater = require('../../../../widget/views/item-repeater')
const repeaterConfig = {
  formView: require('./item-repeater/form'),
  itemView: require('./item-repeater/item'),
  itemModel: require('../models/item-repeater/item'),
  itemNames: {
    singular: 'Image',
    plural: 'Images'
  }
}

module.exports = BaseWidgetView.extend({
  template,

  debug,

  initialize() {
    BaseWidgetView.prototype.initialize.apply(this, arguments)

    const images = this.model.get('images').filter((img) => {
      if (img.images.widgets && img.images.widgets.length) return img
    })

    const itemRepeater = new ItemRepeater(
      this.options.serviceLocator,
      repeaterConfig,
      images
    )
    this.itemRepeater = itemRepeater
    this.$el.find('.js-item-repeater').append(itemRepeater.render().$el)
  },

  addCustomFormData(formData) {
    return BaseWidgetView.prototype.addCustomFormData.call(
      this,
      Object.assign({}, formData, {
        images: this.itemRepeater.getItems(),
        crops
      })
    )
  }
})
