const init = (serviceLocator, done) => {
  const widget = {
    editView: require('./views/form'),
    model: require('./models/model'),
    name: 'Logo Grid',
    itemView: require('./views/item'),
    description: ''
  }

  for (const widgetType of [
    'articleBody',
    'section',
    'event',
    'eventUmbrella',
    'eventArticle',
    'eventSponsorBody'
  ]) {
    serviceLocator.widgetFactories.get(widgetType).register('logoGrid', widget)
  }

  done()
}

module.exports = () => ({ logoGridWidget: ['widget', 'sectionAdmin', init] })
