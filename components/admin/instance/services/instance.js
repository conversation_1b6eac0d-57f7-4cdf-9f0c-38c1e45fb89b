const CrudService = require('../../../../admin/source/js/lib/crud-service')
// This is browserified and doesn't have the URL constructor
/* eslint-disable node/no-deprecated-api */
const { parse } = require('url')

class InstanceService extends CrudService {
  get name() {
    return 'InstanceService'
  }

  get urlRoot() {
    return '/instances'
  }

  getUrl(instance, cb) {
    this.serviceLocator.accountService.read(
      instance.account,
      (err, account) => {
        if (err) return cb(err)
        cb(null, this.createUrl(instance, account))
      }
    )
  }

  createUrl(instance, account) {
    const parsed = parse(this.serviceLocator.config.url)
    let portPostfix = ''
    if (parsed.port) portPostfix = ':' + parsed.port
    return 'http://' + this.createHost(instance, account) + portPostfix
  }

  createHost(instance, account) {
    return instance.subdomain + (account.domain ? '.' + account.domain : '')
  }

  deleteAllSegments(cb) {
    this.authedRequest(
      'POST',
      this.urlRoot + '/delete-all-segments',
      {},
      (err, res, body) => {
        if (err) return cb(err)
        if (res.statusCode >= 300)
          return this.handleError(res.statusCode, body, cb)
        cb(null, body)
      }
    )
  }

  insertMany(data, cb) {
    this.authedRequest(
      'POST',
      this.urlRoot + '/insert-many-segments',
      data,
      (err, res, body) => {
        if (err) return cb(err)
        if (res.statusCode >= 300)
          return this.handleError(res.statusCode, body, cb)
        cb(null, body)
      }
    )
  }
}

module.exports = InstanceService
