.page-content

  .toolbar
    .centering.js-toolbar
      .toolbar__left
        .control-group
          .btn-group
            if (allowed(name.resource, 'delete'))
              button.btn.js-delete(type='button') Delete Selected
          .btn-group
            a.btn.btn.dropdown-toggle(data-toggle='dropdown')
              | Selection (
              span.js-selection-count 0 items
              | ) 
              span.caret
            ul.dropdown-menu
              li
                a.js-select-all Add visible to selection
              li
                a.js-select-none Clear selection

      if (allowed(name.resource, 'create'))
        button.btn.btn--action.js-new(type='button') New #{name.singular}
        button.btn.btn--success.js-sync-segments Sync DMP Segments

  .centering

    header.page-header
      h1= name.plural

    .grid.grid--reverse
      .grid__item.one-quarter
        .js-filters

      .grid__item.three-quarters

        .list-container
          .js-controls

          .grid.list-grid.js-items
          .pagination
            p
              | Showing 
              b.js-item-count
              |  of 
              b.js-total-item-count
              |  items
            button.btn.js-more(type='button') Load more

