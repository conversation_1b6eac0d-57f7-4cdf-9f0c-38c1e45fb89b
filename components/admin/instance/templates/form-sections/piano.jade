.panel.panel-styled
  .panel-header
    h2 Piano Configuration
  .panel-content

    .form-row.form-row-boolean(id='field--pianoEnabled', data-field='pianoEnabled')
      label
        span.form-label-text Piano Enabled?
        .form-field
          input.control.control--boolean(type='checkbox', name='pianoEnabled' checked=data.pianoEnabled)
          span Tick this box to enable Piano functionality on the site.
      .js-error

    .form-row(id='field--pianoApplicationId', data-field='pianoApplicationId')
      label
        span.form-label-text Piano Application ID
        input.control.control--text.form-field(type='text', name='pianoApplicationId', value=data.pianoApplicationId)
      .js-error
      .form-row-description.form-copy
        p Unique ID for the website on Piano

    .form-row(id='field--pianoApiToken', data-field='pianoApiToken')
      label
        span.form-label-text Piano API Token
        input.control.control--text.form-field(type='text', name='pianoApiToken', value=data.pianoApiToken)
      .js-error
      .form-row-description.form-copy
        p API token for connecting the website to Piano

    .form-row(id='field--pianoSubscriptionId', data-field='pianoSubscriptionId')
      label
        span.form-label-text Piano Subscription ID
        input.control.control--text.form-field(type='text', name='pianoSubscriptionId', value=data.pianoSubscriptionId)
      .js-error
      .form-row-description.form-copy
        p ID for the subscription resource for this site. We use this to check to see if someone is a subscriber.

     .form-row.form-row-boolean(id='field--pianoDmpSiteGroupId', data-field='pianoDmpSiteGroupId')
      label
        span.form-label-text Piano DMP Audience Site Group ID
        .form-field
          input.control.control--text.form-field(type='text', name='pianoDmpSiteGroupId', value=data.pianoDmpSiteGroupId)
      .js-error
      .form-row-description.form-copy
        p ID for the DMP Audience Site Group the site is within