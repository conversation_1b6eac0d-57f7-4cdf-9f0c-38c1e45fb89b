const BaseListView = require('./instance-base-list')
const FilterView = require('./list-filter')
const ListItemView = require('./list-item')
const modal = require('modal')

class ListView extends BaseListView {
  constructor(serviceLocator, collection, paginationModel, accounts) {
    super(...arguments)
    this.serviceLocator = serviceLocator
    this.accounts = accounts
    this.$el.on('click', '.js-sync-segments', this.syncDmpSegments.bind(this))
    this.isSegmentsLoading = false
  }

  get name() {
    return { singular: 'Instance', plural: 'Instances', resource: 'instance' }
  }

  get FilterView() {
    return FilterView
  }

  get ListItemView() {
    return ListItemView
  }

  addListItem(model) {
    var listItem = new this.ListItemView(
      this.serviceLocator,
      model,
      this.accounts
    )
    this.listenTo(listItem, 'edit', this.emit.bind(this, 'edit', model.id))
    this.listenTo(
      listItem,
      'showRevisions',
      this.emit.bind(this, 'showRevisions', model)
    )
    this.listenTo(
      listItem,
      'duplicate',
      this.emit.bind(this, 'duplicate', model)
    )
    this.attachView(listItem)
    this.$el.find('.js-items').append(listItem.render().$el)
  }

  getInstances(cb) {
    this.serviceLocator.instanceService.find(
      '',
      {},
      ['name'],
      { pageSize: 1000, projection: ['_id', 'name', 'pianoDmpSiteGroupId'] },
      (err, res) => {
        if (err) return cb(err)
        return cb(null, res.results)
      }
    )
  }

  // Generated by claude.ai
  toUnderscoreHyphen(text) {
    if (!text) return ''

    return (
      text
        // Replace spaces and special characters with underscore
        .replace(/[^a-zA-Z0-9_-]/g, '_')
        // Replace consecutive hyphens with a single hyphen
        .replace(/-+/g, '_')
        // Insert underscore between number-letter and letter-number transitions
        .replace(/([0-9])([a-zA-Z])/g, '$1_$2')
        .replace(/([a-zA-Z])([0-9])/g, '$1_$2')
        // Convert camelCase to underscore
        .replace(/([a-z])([A-Z])/g, '$1_$2')
        // Convert everything to lowercase
        .toLowerCase()
        // Remove leading/trailing hyphens
        .replace(/^-+|-+$/g, '')
    )
  }

  syncDmpSegments() {
    this.isSegmentsLoading = true
    this.updateSyncSegmentsButton(this.isSegmentsLoading)
    if (typeof window !== 'undefined') {
      var cX = window.cX || { options: { consent: true } }
      cX.callQueue = cX.callQueue || []
      // eslint-disable-next-line no-console
      this.serviceLocator.logger.info('cx', cX)
      cX.callQueue.push([
        'invoke',
        () => {
          // eslint-disable-next-line no-console
          this.serviceLocator.logger.info('invoke')

          this.getInstances(async (err, instances) => {
            if (err) {
              this.isSegmentsLoading = false
              this.updateSyncSegmentsButton(this.isSegmentsLoading)
              this.serviceLocator.logger.error(err, 'Could not load instances')
              return modal({
                title: 'Sync Error',
                content: `Error syncing DMP Segments: ${err.message}`,
                buttons: [{ text: 'Dismiss', className: 'btn' }]
              })
            }

            // eslint-disable-next-line no-console
            this.serviceLocator.logger.info('instances', instances)

            let siteGroupIds
            if (instances && instances.length) {
              for (let i = 0; i < instances.length; i++) {
                if (instances[i].pianoDmpSiteGroupId) {
                  siteGroupIds = siteGroupIds || []
                  siteGroupIds.push(instances[i].pianoDmpSiteGroupId)
                }
              }
            }

            // eslint-disable-next-line no-console
            this.serviceLocator.logger.info('siteGroupids: ', siteGroupIds)

            // use jsonpRequest
            const segmentReadCalls = siteGroupIds.map(async (siteGroupId) => {
              return new Promise((resolve, reject) => {
                const segmentReadApiUrl =
                  'https://api.cxense.com/segment/read' +
                  '?callback={{callback}}' +
                  '&persisted=' +
                  encodeURIComponent(
                    'b459acd5c62291aa5231931e8f795e254c28dbff'
                  ) +
                  '&json=' +
                  encodeURIComponent(
                    cX.JSON.stringify({ siteGroupId, showShortId: true })
                  )

                window.cX.jsonpRequest(segmentReadApiUrl, (data) => {
                  if (data.httpStatus && data.httpStatus === 200) {
                    const segments = data.response.segments
                    const formattedSegments = segments.map((s) => ({
                      _id: s.id,
                      name: s.name,
                      group: s.group,
                      id: s.id,
                      shortId: s.shortId,
                      CLIENT__formattedGroup: s.group
                        ? this.toUnderscoreHyphen(s.group)
                        : 'ungrouped'
                    }))
                    resolve(formattedSegments)
                  } else {
                    reject(
                      new Error(
                        `Failed to fetch segments for siteGroupId: ${siteGroupId}`
                      )
                    )
                  }
                })
              })
            })

            await Promise.all(segmentReadCalls)
              .then((allSegments) => {
                this.isSegmentsLoading = false
                this.updateSyncSegmentsButton(this.isSegmentsLoading)
                this.serviceLocator.logger.info('Success: ', allSegments)
                // Calculate total segments for debugging if needed
                // const segmentCount = allSegments.reduce(
                //   (acc, segments) => acc + segments.length,
                //   0
                // )

                // Hmmm the con to doing this on the client is that I'll need to batch my requests.
                // First we're going to delete all the segments
                this.serviceLocator.instanceService.deleteAllSegments((err) => {
                  if (err) {
                    this.serviceLocator.logger.error(
                      err,
                      'Could not delete all segments'
                    )
                    return modal({
                      title: 'Sync Error',
                      content: `Error deleting DMP Segments ❌`,
                      buttons: [{ text: 'Dismiss', className: 'btn' }]
                    })
                  }

                  // Next we're going to insetMany per instance
                  const insertSegmentCalls = allSegments.map(
                    async (segments) => {
                      return new Promise((resolve, reject) => {
                        this.serviceLocator.instanceService.insertMany(
                          segments,
                          (err, res) => {
                            if (err) return reject(err)
                            return resolve(res)
                          }
                        )
                      })
                    }
                  )

                  Promise.all(insertSegmentCalls)
                    .then((res) => {
                      this.serviceLocator.logger.info('Success: ', res)
                      const successCount = res.reduce(
                        (acc, r) => acc + r.successCount,
                        0
                      )
                      modal({
                        title: 'Sync Success',
                        content: `DMP Segments synced successfully ✅. (Segment Count: ${successCount})`,
                        buttons: [{ text: 'Dismiss', className: 'btn' }]
                      })

                      // Create indexes
                    })
                    .catch((err) => {
                      this.serviceLocator.logger.error(err)
                      modal({
                        title: 'Sync Error',
                        content: `Failed to insert segments ❌`,
                        buttons: [{ text: 'Dismiss', className: 'btn' }]
                      })
                    })
                })
              })
              .catch((err) => {
                this.serviceLocator.logger.error(err)
                modal({
                  title: 'Sync Error',
                  content: `Failed to get all segments ❌`,
                  buttons: [{ text: 'Dismiss', className: 'btn' }]
                })
              })
          })
        }
      ])
    }
  }

  updateSyncSegmentsButton(isLoading) {
    if (isLoading) {
      this.$el.find('.js-sync-segments').prop('disabled', true)
      this.$el.find('.js-sync-segments').addClass('btn--loading')
    } else {
      this.$el.find('.js-sync-segments').prop('disabled', false)
      this.$el.find('.js-sync-segments').removeClass('btn--loading')
    }
  }
}

module.exports = ListView
