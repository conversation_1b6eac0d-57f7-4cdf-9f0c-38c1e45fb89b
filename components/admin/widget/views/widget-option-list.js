const compileJade = require('browjadify-compile')
const join = require('path').join
const debug = require('debug')('widget option list view')
const template = compileJade(
  join(__dirname, '/../templates/widget-option-list.jade')
)
const placeholderTemplate = compileJade(
  join(__dirname, '/../templates/widget-placeholder.jade')
)
const primaryWidgets = ['text', 'inlineImage', 'video']

module.exports = window.Backbone.View.extend({
  events: {
    'click .js-widget-list-item': 'handleAdd',
    'click .js-toggle-more-widgets': 'handleToggleMoreWidgets'
  },

  initialize({ collection }) {
    this.collection = collection
    this.render()
  },

  className: 'widget-list js-widget-option-list',

  handleAdd(e) {
    const { target } = e
    const { dataset } = target
    const { type } = dataset

    if (!type) {
      debug('handleAdd ignored, no data-type')
      return
    }

    this.addPlaceholder(type)
    debug('handleAdd', { type })
    this.trigger('add', type)
  },

  handleToggleMoreWidgets() {
    this.$('.label--more').toggleClass('hidden')
    this.$('.label--less').toggleClass('hidden')
    this.$('.js-more-widgets').toggleClass('hidden')
  },

  addPlaceholder(type) {
    // If these cases aren't handled properly, the placeholder is inserted inside the <div> of a js-widget
    if (['insertBefore', 'insertAfter'].includes(this.actionReference)) {
      return $(placeholderTemplate({ type }))[this.actionReference](
        this.$target
      )
    }
    return this.$target[this.actionReference](placeholderTemplate({ type }))
  },

  removeFromView($view) {
    $view.find(this.el).remove()
  },

  prependTo($target) {
    debug('prepending view to target')
    this.$target = $target
    this.actionReference = 'prepend'
    $target.prepend(this.el)
    this.delegateEvents()
  },

  appendTo($target) {
    debug('appending view to target')
    this.$target = $target
    this.actionReference = 'append'
    $target.append(this.el)
    this.delegateEvents()
  },

  insertBefore($target) {
    debug('inserting view before target')
    this.$target = $target
    this.actionReference = 'insertBefore'
    this.$el.insertBefore($target)
    this.delegateEvents()
  },

  getWidgetsList(type) {
    const widgets = this.collection.toArray()
    return type === 'primary'
      ? widgets
          .filter((widget) => primaryWidgets.includes(widget.get('type')))
          .reverse()
      : widgets
          .filter((widget) => !primaryWidgets.includes(widget.get('type')))
          .reverse()
  },

  insertAfter($target) {
    debug('inserting view after target')
    this.$target = $target
    this.actionReference = 'insertAfter'
    this.$el.insertAfter($target)
    this.delegateEvents()
  },

  render() {
    this.$el.html(
      template({
        primaryWidgets: this.getWidgetsList('primary'),
        moreWidgets: this.getWidgetsList('secondary')
      })
    )
    return this
  }
})
