const compileJade = require('browjadify-compile')
const join = require('path').join
const template = compileJade(join(__dirname, '/../../templates/item/base.jade'))
const debug = require('../../../../../admin/source/js/lib/debug')(
  'base widget item view'
)
const modal = require('modal')
const widgetExpander = require('../../lib/widget-expander-toggle')(debug)

module.exports = window.Backbone.View.extend({
  className: 'js-widget widget-grid__item',
  template,
  events: {
    'click .js-remove': 'handleRemove',
    'click .js-expander-toggle': 'handleExpanderToggle',
    'click .js-duplicate': 'handleDuplicate',
    'click .js-copy-to-clipboard': 'handleCopy',
    'click .js-paste-after': 'handlePaste'
  },
  initialize(options) {
    this.options = options
    debug('init')

    this.extraProperties = {}

    if (!this.factory) {
      throw new Error('You need to extend this view and provide a factory')
    }
    if (!this.template) {
      throw new Error('You need to extend this view and provide a template')
    }
    this.render()
    this.model.on('change', (model, { silent } = {}) => {
      debug('model changed', model)
      if (!silent) this.render()
    })

    this.model.on('save', (model) => {
      debug('model changed', model)
      this.render()
    })

    this.on('init', this.handleEdit, this)

    this.$el.attr('data-id', this.model.id)
    this.$el.attr('data-cid', this.model.cid)
  },

  handleRemove() {
    debug('remove', this.model.get('id'))

    modal({
      content: 'Do you want to delete this widget?',
      buttons: [
        { text: 'Cancel', event: 'cancel', className: 'btn', keyCodes: [27] },
        { text: 'Delete', event: 'confirm', className: 'btn btn--error' }
      ]
    }).on('confirm', () => {
      debug('remove widget confirmed')
      this.model.trigger('remove', this.model)
    })
  },

  handleExpanderToggle: widgetExpander.toggleExpander,

  handleDuplicate() {
    debug('duplicate', this.model.get('id'))
    this.model.trigger('duplicate', this.model)
  },

  handleCopy() {
    debug('copyToClipboard', this.model.get('id'))
    this.model.trigger('copyToClipboard', this.model)
    this.$el.find('.js-paste-after').removeAttr('style')
  },

  handlePaste() {
    debug('pasteAfter', this.model.get('id'))
    this.model.trigger('pasteAfter', this.model)
    this.$el
      .find('.js-paste-after')
      .attr('style', 'pointer-events: none; opacity: .5;')
  },

  render() {
    debug('rendering widget')

    this.$el.empty().append(
      this.template(
        Object.assign(
          {
            name: this.factory.name,
            description: this.factory.description,
            options: this.factory.options,
            model: this.model,
            id: this.model.id
          },
          this.extraProperties
        )
      )
    )

    this.$('.js-tooltip-trigger').tooltip({ html: true })

    const model =
      JSON.parse(localStorage.getItem('widget_copy_to_clipboard')) || {}

    if ('type' in model) {
      this.$el.find('.js-paste-after').removeAttr('style')
    }

    return this
  }
})
