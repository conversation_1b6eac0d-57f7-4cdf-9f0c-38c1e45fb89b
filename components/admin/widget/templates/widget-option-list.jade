.js-primary-widgets
  each widget in primaryWidgets
    .widget-list-item.js-widget-list-item(data-type=widget.get('type'))
      i.icon.icon--plus
      | #{widget.get('name')}

  .js-more-widgets.more-widgets-list.hidden
    each widget in moreWidgets
      .widget-list-item.js-widget-list-item(data-type=widget.get('type'))
        i.icon.icon--plus
        | #{widget.get('name')}

  .btn.btn--success.js-toggle-more-widgets
    i.icon.icon--plus
    span.label--more  More
    span.label--less.hidden  Less
