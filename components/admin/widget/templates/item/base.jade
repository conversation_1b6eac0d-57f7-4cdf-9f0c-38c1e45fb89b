.widget
  .expander.js-expander.expander-expanded
    block header
      .expander-header.list-item-header
        if (!model.isOnlyEditable)
          .list-item-leading-actions
            span.label.label--large.sort-handle.js-sort-handle
              i.icon.icon--hamburger Move
        .list-item-actions
          .control-group
            block status

          if (!model.isOnlyEditable)
            .control-group
              .btn-group
                block actions
                  a.btn.btn--small.dropdown-toggle(data-toggle='dropdown', id=id)
                    span.caret
                  ul.dropdown-menu.pull-right
                    li
                      a.js-expander-toggle(data-text-expand='Expand', data-text-collapse='Collapse') Collapse
                    li
                      a.js-remove Delete
                    li
                      //a.js-duplicate Duplicate
                      hr
                    li
                      a.js-copy-to-clipboard Copy
                    li
                      a.js-paste-after(style='pointer-events: none; opacity: .5;') Paste 

        block name
          h2= name

    block content
      if description
        .list-item-content.expander-content.space-between
          p= description
          p.id= model.get('id')
