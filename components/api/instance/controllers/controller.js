const crudServiceApiBuilder = require('cf-crud-service-api-builder')
const searchEndpointBuilder = require('../../../../api/lib/search-endpoint-builder')
const authentication = require('cf-auth-middleware')
const authorisation = require('../../../../api/lib/middleware/authorisation')
const authUserResolver = require('../../../../api/lib/middleware/authenticated-user-resolver')
const accountFilter = require('../../account/lib/middleware/account-filter')
const revisionCreator = require('../../revision/lib/create-new-revision')
const instanceFilter = require('../../instance/lib/middleware/instance-filter')

module.exports = (serviceLocator) => {
  const authenticationMiddleware = authentication(
    serviceLocator.authenticationProvider,
    { defaultTtl: process.env.API_AUTH_TIMEOUT || 60000 }
  )
  const authorisationMiddleware = authorisation(
    serviceLocator,
    '/instances',
    'instance'
  )
  const authUserLookupMiddleware = authUserResolver(serviceLocator)
  const accountFilterMiddleware = accountFilter(serviceLocator)
  const instanceFilterMiddleware = instanceFilter(serviceLocator, {
    field: '_id'
  })
  const router = serviceLocator.router
  const createNewRevision = revisionCreator(serviceLocator, 'instance')

  const collectionExists = async (db, collectionName) => {
    const collections = await db
      .listCollections({ name: collectionName })
      .toArray()
    return collections.length > 0
  }

  router.post(
    '/instances/delete-all-segments',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware,
      instanceFilterMiddleware
    ],
    async (req, res) => {
      try {
        const collectionName = 'temp_dmp_segments'
        serviceLocator.logger.info(
          '(/instances/delete-all-segments) collectionname: ',
          collectionName
        )
        const deleteCount = await serviceLocator.serviceDatabase
          .collection(collectionName)
          .count()
        serviceLocator.logger.info(
          '(/instances/delete-all-segments) deleteCount',
          deleteCount
        )
        if (
          await collectionExists(serviceLocator.serviceDatabase, collectionName)
        ) {
          await serviceLocator.serviceDatabase.collection(collectionName).drop()
        }
        return res
          .status(200)
          .json({ message: `All segments deleted (${deleteCount} deleted)` })
      } catch (err) {
        return res.status(500).json({ error: `delete error: ${err.message}` })
      }
    }
  )

  router.post(
    '/instances/insert-many-segments',
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      accountFilterMiddleware,
      instanceFilterMiddleware
    ],
    async (req, res) => {
      const collectionName = 'temp_dmp_segments'
      let insertedCount = 0
      try {
        const result = await serviceLocator.serviceDatabase
          .collection(collectionName)
          .insertMany(req.body, { ordered: false })
        insertedCount = result.insertedCount
      } catch (err) {
        serviceLocator.logger.warn(
          'Some segments were not inserted due to duplicates:',
          err.message
        )
        insertedCount = err.result?.nInserted || 0
      } finally {
        // eslint-disable-next-line no-unsafe-finally
        return res.status(200).json({
          message: `Segments processed. Successfully inserted: ${insertedCount} out of ${req.body.length}`,
          successCount: insertedCount
        })
      }
    }
  )

  const api = crudServiceApiBuilder(
    serviceLocator.instanceService,
    '/instances',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware,
      instanceFilterMiddleware
    ],
    ['post', 'put', 'patch', 'delete']
  )

  api.on('create', createNewRevision)
  api.on('update', createNewRevision)
  api.on('partialUpdate', createNewRevision)

  searchEndpointBuilder(
    serviceLocator.instanceService,
    '/instances',
    router,
    serviceLocator.logger,
    [
      authenticationMiddleware,
      authUserLookupMiddleware,
      authorisationMiddleware,
      accountFilterMiddleware,
      instanceFilterMiddleware
    ]
  )
}
