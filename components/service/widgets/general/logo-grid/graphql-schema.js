const schemata = require('@clocklimited/schemata')
const createImageSchema = require('./image-schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createGraphqlImageBuilderProperty = require('../../../asset/lib/graphql-image-builder-property')

const createSchema = (serviceLocator) => {
  const createGraphqlImageSchema = () => {
    const typeName = 'LogoGridItem'
    const imageVariations = [
      {
        name: 'logo_free_640',
        crop: 'Free',
        context: 'Logo',
        size: { width: 640 }
      }
    ]

    return schemata({
      name: typeName,
      properties: {
        images: createGraphqlImageBuilderProperty(
          serviceLocator,
          imageVariations,
          typeName
        ),
        crop: {
          type: String
        }
      }
    })
  }

  return schemata({
    name: 'LogoGridWidget',
    properties: {
      images: {
        type: schemata.Array(
          extendSchemata(createImageSchema(), createGraphqlImageSchema())
        )
      }
    }
  })
}

module.exports = createSchema
