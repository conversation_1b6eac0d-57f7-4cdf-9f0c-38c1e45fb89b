const schemata = require('@clocklimited/schemata')
const createContextValidator = require('validity-cf-image-context-selection')
const createWidgetAreaSchema = require('../../../widget/widget-area-schema')
const createImageWidgetSchema = require('../../../asset/image-schema')
const createCropValidator = require('../../../../../lib/validators/crop-integrity-validator')
const imageConfig = require('../../../../admin/widgets/general/logo-grid/image-config.json')
const createImageCaptionValidator = require('../../../../../lib/validators/image-caption-validator')

const requiredCrops = imageConfig.crops.map((crop) => crop.name)

const createSchema = () =>
  schemata({
    name: 'LogoGridItem',
    properties: {
      images: {
        type: createWidgetAreaSchema(createImageWidgetSchema()),
        defaultValue: () => ({}),
        validators: {
          all: [
            createContextValidator(['Logo']),
            createCropValidator(requiredCrops),
            createImageCaptionValidator()
          ]
        }
      }
    }
  })

module.exports = createSchema
