const createSchema = require('./schema')
const extendSchemata = require('../../../../../api/lib/schemata-extender')
const createListGraphqlSchema = require('../list/graphql-schema')
const createWidgetSchema = require('../../../widget/widget-schema')

const createGraphqlSchema = (serviceLocator) => {
  const schema = createSchema(serviceLocator)
  const listGraphqlSchema = createListGraphqlSchema(
    serviceLocator,
    'SimpleArticleGridWidget'
  )
  const extendedSchema = extendSchemata(schema, listGraphqlSchema)

  extendedSchema.implements = [createWidgetSchema()]
  extendedSchema.isTypeOf = (item) => item.type === 'simpleArticleGrid'

  return extendedSchema
}

module.exports = createGraphqlSchema
