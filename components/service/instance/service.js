const { promisify } = require('util')
const cachedCrudService = require('../lib/cached-crud-service')
const crudService = require('crud-service')
const textSearch = require('@clocklimited/cf-text-search')
const createSchema = require('./schema')
const createEntitySection = require('../section/lib/entity-section-creator')
const createSectionSchema = require('../../service/section/schema')
const createDefaultCategorySections = require('./lib/default-category-section-creator')
const renameOldCategoriesToNewCategories = require('./lib/old-category-to-new-category-renamer')
const renameCategoriesInNavigation = require('./lib/categories-in-navigation-renamer')

module.exports = (serviceLocator) => {
  const save = serviceLocator.persistence('instance')
  const schema = createSchema()
  const service = crudService('Instance', save, schema, {})
  const originalCreate = service.create

  service.findOne = save.findOne
  service.search = textSearch(service)

  service.create = (object, callback) => {
    originalCreate(object, async (error, createdInstance) => {
      if (error) return callback(error)

      const section = {
        instance: createdInstance._id,
        account: createdInstance.account
      }

      if (!createdInstance.duplicatedInstance) {
        try {
          await createDefaultSections(section, createdInstance)
        } catch (error) {
          return callback(error)
        }
      } else {
        try {
          await duplicateInstance(section, createdInstance)
        } catch (error) {
          return callback(error)
        }
      }

      callback(null, createdInstance)
    })
  }

  service.readByExternalId = (account, id, cb) => {
    service.find(
      {
        externalId: id,
        account: account._id
      },
      (err, data) => {
        if (err) return cb(err)
        cb(null, data[0])
      }
    )
  }

  service.resolveUserTargetingValues = (idObjects, cb) => {
    // Take the ids and find in collection temp_dmp_segments
    const ids = idObjects.map((idObject) => idObject.id)
    const findShortId = (id) =>
      idObjects.find((idObject) => idObject.id === id).shortId

    serviceLocator.serviceDatabase
      .collection('temp_dmp_segments')
      .find({ _id: { $in: ids } })
      .toArray((err, segments) => {
        if (err) return cb(err)

        // Log segments for debugging if needed
        // serviceLocator.logger.debug('segments: ', segments)

        const targeting = segments.reduce((acc, segment) => {
          if (!acc[segment.CLIENT__formattedGroup]) {
            acc[segment.CLIENT__formattedGroup] = [findShortId(segment._id)]
          } else {
            acc[segment.CLIENT__formattedGroup].push(findShortId(segment._id))
          }
          return acc
        }, {})

        // next step is to create a resolveUserTargetingValues mutation / query, which uses this function and bish bosh bash.
        // I also need to create a query to get all instance siteGroupIds but that should be easy enough
        cb(null, targeting)
      })
  }

  const createDefaultSections = async (sectionProperties, createdInstance) => {
    const defaultSections = [
      Object.assign(createSectionSchema().makeDefault(sectionProperties), {
        name: 'Home',
        root: true
      })
    ].concat(
      serviceLocator.entitySectionLayoutFactory
        .list()
        .map((layout) =>
          createEntitySection(
            sectionProperties,
            layout,
            serviceLocator.entitySectionLayoutFactory.get(layout)
          )
        )
    )

    await createDefaultCategorySections(
      sectionProperties,
      createdInstance,
      defaultSections,
      serviceLocator
    )

    await Promise.all(
      defaultSections.map(async (defaultSection) =>
        promisify(serviceLocator.sectionService.create)(defaultSection)
      )
    )
  }

  const duplicateInstance = async (sectionProperties, createdInstance) => {
    const duplicatedFromInstance = await promisify(
      serviceLocator.instanceService.read
    )(createdInstance.duplicatedInstance)

    const parentSections = await promisify(serviceLocator.sectionService.find)({
      instance: createdInstance.duplicatedInstance,
      parent: null
    })

    const createParentAndChild = async (section) => {
      const newSection = Object.assign({}, sectionProperties, section)

      delete newSection._id
      newSection.previewId = Math.round(Math.random() * 100000000000).toString(
        36
      )
      newSection.instance = createdInstance._id
      newSection.createdDate = new Date()

      // rename old instance "category" sections to new instance "category" sections
      if (duplicatedFromInstance) {
        renameOldCategoriesToNewCategories(
          newSection,
          duplicatedFromInstance,
          createdInstance
        )
      }

      const savedParent = await promisify(serviceLocator.sectionService.create)(
        newSection
      )
      const childrenToDuplicate = await promisify(
        serviceLocator.sectionService.find
      )({ parent: section._id })

      if (childrenToDuplicate.length === 0) return

      await Promise.all(
        childrenToDuplicate.map(async (child) => {
          child.parent = savedParent._id
          await createParentAndChild(child)
        })
      )
    }

    await Promise.all(
      parentSections.map(async (sectionToDuplicate) =>
        createParentAndChild(sectionToDuplicate)
      )
    )

    const createdParentSections = await promisify(
      serviceLocator.sectionService.find
    )({
      instance: createdInstance._id
    })

    // update navigation for category changes
    if (duplicatedFromInstance) {
      await renameCategoriesInNavigation(
        createdInstance,
        duplicatedFromInstance,
        createdParentSections,
        parentSections,
        serviceLocator
      )
    }
  }

  return cachedCrudService(serviceLocator, service)
}
