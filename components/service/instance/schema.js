const schemata = require('@clocklimited/schemata')
const required = require('@clocklimited/validity-required')
const validateLength = require('validity-length')
const isEmail = require('@clocklimited/validity-email')
const hasWidgets = require('../../../lib/has-widgets')
const validateIf = require('@clocklimited/validity-validate-if')
const createKeyValueTargetingSchema = require('../widgets/general/advert/key-value-schema')
const createContextValidator = require('validity-cf-image-context-selection')
const createCropValidator = require('../../../lib/validators/crop-integrity-validator')
const imageConfig = require('../article/image-config.json')

module.exports = () => {
  const requiredContexts = ['Thumbnail', 'Hero', 'Share Image']
  const requiredCrops = imageConfig.crops.map((crop) => crop.name)
  return schemata({
    name: 'Instance',
    properties: {
      _id: {
        type: String
      },
      name: {
        type: String,
        validators: { all: [required] }
      },
      account: {
        type: String,
        validators: { all: [required] },
        private: true
      },
      subdomain: {
        type: String,
        validators: { all: [required] }
      },
      disableRegionPicker: {
        type: Boolean,
        defaultValue: false
      },
      enabled: {
        type: Boolean,
        defaultValue: true,
        validators: { all: [] }
      },
      strapline: {
        type: String,
        validators: { all: [] }
      },
      logo: {
        type: String,
        validators: { all: [required] },
        private: true
      },
      lightLogo: {
        type: String,
        validators: { all: [required] },
        private: true
      },
      brandType: {
        type: String,
        validators: { all: [required] }
      },
      facebookId: {
        type: String,
        validators: { all: [] }
      },
      twitterId: {
        type: String,
        validators: { all: [] }
      },
      linkedinId: {
        type: String,
        validators: { all: [] }
      },
      youtubeId: {
        type: String,
        validators: { all: [] }
      },
      instagramId: {
        type: String,
        validators: { all: [] }
      },
      mediumId: {
        type: String,
        validators: { all: [] }
      },
      robotsTxt: {
        type: String,
        validators: []
      },
      footerHtml: {
        type: String,
        validators: { all: [] },
        private: true
      },
      dateCreated: {
        type: Date,
        defaultValue: () => new Date(),
        private: true
      },
      navigation: {
        type: Array,
        private: true
      },
      footerNavigation: {
        type: Array,
        private: true
      },
      footerNavigationInstances: {
        type: Array,
        defaultValue: () => [],
        private: true
      },
      duplicatedInstance: {
        type: String
      },
      categories: {
        type: Array,
        validators: [validateLength(1)]
      },
      deprecatedCategories: {
        type: Array,
        defaultValue: () => []
      },
      advertSiteId: {
        type: String
      },
      preventAdsOnMobile: {
        type: Boolean
      },
      headerAdvertVisible: {
        type: Boolean
      },
      headerAdvertBelowNav: {
        type: Boolean
      },
      headerAdvertSlotName: {
        type: String
      },
      keyValueTargeting: {
        type: schemata.Array(createKeyValueTargetingSchema())
      },
      holdingPageTitle: {
        type: String
      },
      holdingPageSubtitle: {
        type: String
      },
      holdingImageAlt: {
        type: String
      },
      images: {
        type: Object,
        defaultValue: () => ({ widgets: [] }),
        validators: {
          draft: [
            validateIf(hasWidgets, createContextValidator(requiredContexts)),
            validateIf(hasWidgets, createCropValidator(requiredCrops))
          ],
          published: [
            createContextValidator(requiredContexts),
            createCropValidator(requiredCrops)
          ],
          archived: [
            createContextValidator(requiredContexts),
            createCropValidator(requiredCrops)
          ]
        }
      },
      iconType: {
        type: String
      },
      issuPublicationId: {
        type: String,
        validators: { all: [required] }
      },
      oldBrandId: {
        type: String,
        validators: { all: [required] }
      },
      theme: {
        type: String,
        validators: { all: [required] },
        defaultValue: 'ai'
      },
      primaryColorOverride: {
        type: String,
        validators: []
      },
      secondaryColorOverride: {
        type: String,
        validators: []
      },
      headerType: {
        type: String,
        validators: { all: [required] }
      },
      mailChimpNewsletterAudienceId: {
        type: String,
        validators: [required]
      },
      googleTagManagerId: {
        type: String,
        validators: []
      },
      googleOptimizeContainerId: {
        type: String,
        validators: []
      },
      googleOptimizeAsynchronous: {
        type: Boolean,
        defaultValue: false
      },
      editorialContactFormToEmailAddress: {
        type: String,
        validators: [isEmail]
      },
      salesforceId: {
        type: String,
        defaultValue: ''
      },
      cookieConsentId: {
        type: String
      },
      pianoEnabled: {
        type: Boolean
      },
      pianoApplicationId: {
        type: String
      },
      pianoApiToken: {
        type: String
      },
      pianoSubscriptionId: {
        type: String
      },
      pianoDmpSiteGroupId: {
        type: String
      },
      featureFlags: {
        type: Array,
        defaultValue: () => []
      }
    }
  })
}
