//
// BUTTONS
// =======
//


//
// BASE STYLES
// Base styles to persuade `<a>`, `<button>` and `<input>` elements to look the
// same as each other, across various browsers.
//

.btn
  position relative
  display inline-block
  vertical-align middle
  cursor pointer
  overflow visible // removes padding in IE

  // Common Presentational Styles
  font-family inherit
  font-size rem(14)
  font-weight normal
  line-height rem(18)
  padding 4px 9px
  text-align center
  text-decoration none
  transition all 0.3s ease
  border-radius $base--border-radius

  // 'Default' Presentational Styles
  color $color--black
  border 1px solid shade($color--grey--light, 10%)
  background-color $color--grey--light

  &:focus
  &:hover
    text-decoration none
    outline 0
    transition-duration 0.1s

    color $color--black
    border-color shade($color--grey--light, 25%)
    background-color shade($color--grey--light, 15%)

  &:active
    border-color shade($color--grey--light, 35%)
    background-color shade($color--grey--light, 25%)


  // Button Content

  .icon
    margin-top 2px


//
// STATE MODIFIERS
//

for $type in 'action' 'success' 'notice' 'warning' 'error'
  $color = lookup('$color--' + $type)
  .btn--{$type}
    color $color--white
    border-color shade($color, 10%)
    background-color $color
    text-shadow 0 1px 2px shade($color, 30%)
    &:focus
    &:hover
      color $color--white
      border-color shade($color, 25%)
      background-color shade($color, 15%)
    &:active
      border-color shade($color, 35%)
      background-color shade($color, 25%)


//
// SIZE MODIFIERS
//

.btn--small
  padding 2px 7px
  font-size rem(12)

.btn--large
  padding 7px 9px

.btn--block
  display block
  width 100%

  & + &
    margin-top rem(5)


//
// UNSTYLED BUTTONS
// Simple reset to make button elements look more like text links.
//

.text-btn
  @extend a //@stylint ignore - will be updated as part of button refactor
  padding 0
  margin 0
  border 0
  background none
  cursor pointer
  font inherit

  .icon
    vertical-align middle

.btn-saml
  gap 4px //@stylint ignore
  display inline-flex //@stylint ignore
  border 1px solid shade($color--grey--light, 35%)
  padding 8px
  width 100%
  color $color--white
  background-color $color--black


//
// DISABLED BUTTONS
//

.btn[disabled]
.btn.is-disabled
  opacity 0.6
  pointer-events none
  user-select none


.btn--delete
  background $color--error
  color $color--white
  border-color shade($color--error, 10%)

  &:focus
  &:hover
    text-decoration none
    outline 0
    color $color--white
    transition-duration 0.1s
    border-color shade($color--error, 25%)
    background-color shade($color--error, 15%)

  &:active
    border-color shade($color--error, 35%)
    background-color shade($color--error, 25%)


.btn--preview
  min-width 80px
  background $color--white
  
  &:hover
    background shade($color--white, 10%)
    
.btn--preview.btn--success
  background $color--success
  color $color--white
  border-color shade($color--success, 10%)

  &:hover
    background shade($color--success, 10%)

//
// LOADING STATE
//

.btn--loading
  position relative
  padding-right 35px // Extra space for the spinner
  
  &:after
    content ''
    position absolute
    right 10px
    top 50%
    width 16px
    height 16px
    margin-top -8px
    border 2px solid currentColor
    border-right-color transparent
    border-radius 50%
    animation spin 0.8s linear infinite

@keyframes spin
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
