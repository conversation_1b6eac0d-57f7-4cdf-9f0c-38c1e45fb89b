v1.388.0 / Mon Jul 07 2025 05:38:50 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Updates Telco Favicons

v1.387.0 / Thu Jul 03 2025 09:40:58 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL
- Feat: Logo grid
- Feat: Copy and Pasta

v1.386.0 / Thu Jul 03 2025 09:38:22 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL
- Feat: Logo grid
- Feat: Copy and Pasta

v1.385.0 / Thu Jul 03 2025 08:45:11 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL
- Feat: Logo grid
- Feat: Copy and Pasta

v1.384.0 / Tue Jul 01 2025 14:27:21 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL
- Feat: Logo grid

v1.383.0 / Mon Jun 30 2025 15:18:52 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL
- Feat: Logo grid

v1.382.0 / Mon Jun 30 2025 10:47:11 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: font size
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL

v1.381.0 / Mon Jun 30 2025 10:40:20 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets
- Feat: DOOM SCROLL

v1.380.0 / Mon Jun 30 2025 08:41:45 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets

v1.379.0 / Tue Jun 24 2025 19:25:01 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adestra feed - updates 
- WEB 424 - Ad Unit Renaming #214 (Isolated) #287 + CX Segmnets

v1.378.0 / Tue Jun 24 2025 18:29:26 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adestra feed - updates 

v1.376.0 / Tue Jun 24 2025 15:07:31 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adestra feed - updates 

v1.377.0 / Tue Jun 24 2025 18:04:28 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB-XXX - Event umbrella seo section
- Feat: WEB-XXX - Adds duplicate button for widgets
- Feat: WEB-XXX - Adds simple Instance-level feature flagging
- Feat: WEB-XXX - Adds article auto save

v1.371.0 / Mon Jun 23 2025 10:21:13 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Fix: Adds fallback for event umbrella title
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.370.0 / Mon Jun 23 2025 08:59:09 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Fix: Adds fallback for event umbrella title
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.369.0 / Mon Jun 23 2025 08:55:48 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Fix: Adds fallback for event umbrella title
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.368.0 / Fri Jun 20 2025 13:43:23 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.367.0 / Fri Jun 20 2025 12:13:46 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.366.0 / Fri Jun 20 2025 11:23:05 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.365.0 / Wed Jun 18 2025 15:16:26 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.364.0 / Wed Jun 18 2025 13:23:05 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.363.0 / Wed Jun 18 2025 08:27:40 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.362.0 / Wed Jun 18 2025 08:09:58 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.361.0 / Tue Jun 17 2025 10:59:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.360.0 / Tue Jun 17 2025 08:38:28 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging
- Feat: Adestra rss feed

v1.359.0 / Tue Jun 10 2025 15:16:03 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging

v1.358.0 / Tue Jun 10 2025 14:05:17 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Reduces json payload slowing down page load
- Feat: WEB-XXX - Fixes Event Portfolio og:image + Piano Action hidden field
- Feat: WEB-XXX - Article auto tagging

v1.357.1 / Tue Jun 10 2025 09:43:24 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Martins amends

v1.357.0 / Tue Jun 10 2025 09:27:48 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Fix: WEB-XXX - Corrects places playlist param into autoplay
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles
- Feat: Readd magazine cta to site

v1.356.0 / Mon Jun 09 2025 15:43:46 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Fix: WEB-XXX - Corrects places playlist param into autoplay
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles
- Feat: Readd magazine cta to site

v1.355.0 / Fri Jun 06 2025 15:21:06 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles
- Feat: Readd magazine cta to site

v1.354.0 / Fri Jun 06 2025 15:17:04 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles
- Feat: Readd magazine cta to site

v1.353.0 / Fri Jun 06 2025 14:30:57 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles
- Feat: Readd magazine cta to site

v1.352.0 / Fri Jun 06 2025 12:13:01 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Fix: Martins amends
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles

v1.350.0 / Tue Jun 03 2025 09:14:49 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget
- Feat: Add tool that will add new widgets to articles

v1.349.0 / Mon Jun 02 2025 10:51:02 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital
- Feat: Update event sponsor tier widget

v1.348.0 / Mon Jun 02 2025 09:28:43 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital

v1.347.0 / Fri May 30 2025 13:22:18 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Feat: adestra rss feed
- Feat: WEB-XXX - Updates the favicon for supply chain digital

v1.346.0 / Thu May 29 2025 15:04:26 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour
- Feat: adestra rss feed

v1.345.3 / Thu May 29 2025 14:53:21 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - (HACK) Disables LIVE indicators / buttons due to non-timezone bound live behaviour

v1.345.0 / Tue May 27 2025 14:32:18 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button
- Feat: adestra rss feed

v1.344.0 / Tue May 27 2025 10:50:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button
- Feat: adestra rss feed

v1.343.0 / Tue May 27 2025 09:06:09 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button
- Feat: adestra rss feed

v1.342.0 / Tue May 27 2025 07:51:42 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.341.0 / Sun May 25 2025 10:54:41 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.340.0 / Sun May 25 2025 09:46:06 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Fix: WEB-XXX - Corrects speaker.thumbnail not pulling through
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.339.0 / Fri May 23 2025 12:27:17 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.338.0 / Fri May 23 2025 10:59:13 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.337.0 / Fri May 23 2025 10:45:57 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.336.0 / Fri May 23 2025 09:43:01 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Fix: timeline being out
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.335.0 / Fri May 23 2025 09:25:00 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Fix: Showing draft events, blend mode and same day events
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.334.0 / Thu May 22 2025 16:03:44 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Fix: hero gradiant
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.333.0 / Thu May 22 2025 10:37:00 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Feat: Prevent wrapping of subitems on desktop
- Feat: Move onetrust manage button

v1.333.0 / Thu May 22 2025 10:15:21 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Fix: WEB-XXX - Corrects old incorrect brand logo link
- Feat: Prevent wrapping of subitems on desktop

v1.332.0 / Thu May 22 2025 09:10:20 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Feat: Prevent wrapping of subitems on desktop

v1.331.0 / Thu May 22 2025 08:59:09 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login
- Feat: Prevent wrapping of subitems on desktop

v1.330.3 / Thu May 22 2025 08:46:24 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login

v1.330.2 / Thu May 22 2025 08:35:25 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Adds 3 retries on SAML login

v1.330.1 / Wed May 21 2025 14:48:11 GMT+0000 (Coordinated Universal Time)
====================================
- fix: manual revert of a broken release

v1.329.0 / Tue May 20 2025 14:52:53 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid
- Feat: Event agenda updates

v1.328.0 / Tue May 20 2025 13:51:27 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid
- Feat: Event agenda updates

v1.327.0 / Tue May 20 2025 13:16:35 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid
- Feat: Event agenda updates

v1.326.0 / Tue May 20 2025 12:59:32 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid
- Feat: Event agenda updates

v1.325.0 / Tue May 20 2025 12:47:47 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid
- Feat: Event agenda updates

v1.324.0 / Tue May 20 2025 10:58:47 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid

v1.323.0 / Tue May 20 2025 10:16:51 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid

v1.322.0 / Mon May 19 2025 15:18:14 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta
- Feat: Add carousel to event content grid

v1.321.0 / Mon May 19 2025 09:14:43 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta

v1.320.0 / Fri May 16 2025 14:55:59 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta

v1.319.0 / Fri May 16 2025 13:41:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX: Prevents Event Umbrella Event Content Grid filtering not working
- Fix: WEB-XXX Fixes Event Content Grid & Event Agenda Navigator /live interactions
- Fix: WEB-XXX - Prevents Umbrella /on-demand from Fetching Video Layouts & 500
- Feat: WEB-XXX: Adds extra event field conditional rendering field
- Feat: adestra rss feed
- Feat: WEB-XXX: Adds survey category
- Feat: Event grid widget
- Feat: WEB-XXX Latest magazine CTA
- Feat: WEB-XXXX Latest event cta

v1.301.0 / Mon May 12 2025 11:44:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.300.0 / Mon May 12 2025 09:36:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.299.0 / Fri May 09 2025 15:15:22 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.298.0 / Fri May 09 2025 14:58:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.297.0 / Thu May 08 2025 15:12:26 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.296.0 / Thu May 08 2025 14:39:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards
- Feat: Many UX amends

v1.295.0 / Thu May 08 2025 11:55:11 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout
- Feat: Add all speaker events below cards

v1.294.0 / Thu May 08 2025 07:55:17 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout

v1.293.0 / Tue May 06 2025 14:36:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet
- Feat: Mobile friendly layout

v1.292.0 / Tue May 06 2025 11:35:37 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet

v1.291.0 / Tue May 06 2025 10:24:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet

v1.290.0 / Tue May 06 2025 09:57:43 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Fix: Update footer url
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet

v1.289.0 / Tue May 06 2025 08:57:22 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Fix: Issue with nested smartlink on ButtonGroup component
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet

v1.288.0 / Fri May 02 2025 20:14:29 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Fix: WEB-XXX: Purposely blocks of Portfolio pages for the time being
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size
- Feat: WEB-XXX - Event article grid snippet

v1.287.0 / Fri May 02 2025 10:18:52 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip
- Feat: Adjust body font size

v1.286.0 / Fri May 02 2025 08:59:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip

v1.285.0 / Fri May 02 2025 08:36:39 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories
- Feat: Add the option to make text and logos black on sponsor strip

v1.284.0 / Wed Apr 30 2025 16:53:20 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories

v1.283.0 / Wed Apr 30 2025 13:39:28 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Fix: WEB-XXX: Event Content Block tweaks
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass
- Feat: WEB-XXX - Adds Depreciated Article Categories

v1.282.0 / Tue Apr 29 2025 18:54:15 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds Special Actions (refactors previous implementation)
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass

v1.281.0 / Tue Apr 29 2025 17:57:13 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting
- Feat: WEB-XXX: Adds flags to event page health checker script to allow for cache bypass

v1.280.1 / Tue Apr 29 2025 17:34:57 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents Data from Sourced Duplicate Article from Persisting

v1.280.0 / Tue Apr 29 2025 13:57:09 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.279.0 / Tue Apr 29 2025 13:28:17 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.278.0 / Tue Apr 29 2025 13:15:12 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.277.0 / Tue Apr 29 2025 13:03:26 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Adds Event Presets

v1.276.0 / Tue Apr 29 2025 12:52:18 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Agenda navigator

v1.275.0 / Tue Apr 29 2025 12:46:01 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Agenda navigator

v1.274.0 / Tue Apr 29 2025 12:34:50 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX: Changes sponsor within schema.org to partner (again)
- Fix: Readd Cookiepro script
- Fix: Call to property on null
- Fix: Adds option to increase image size on sponsor strip
- Fix: Advert unit offscreen on smaller viewport
- Feat: WEB2025-1118 - Event sponsor widgets
- Feat: WEB-XXX: Updates Procurement Magazine Favicons

v1.273.0 / Mon Apr 28 2025 16:46:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Fix WEB-XXX: Changes sponsor within schema.org to partner (again)
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds
- Feat: WEB-XXX: Updates Procurement Magazine Favicons

v1.272.0 / Mon Apr 28 2025 16:21:42 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds
- Feat: WEB-XXX: Updates Procurement Magazine Favicons

v1.271.0 / Mon Apr 28 2025 16:09:31 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.270.0 / Mon Apr 28 2025 13:39:09 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.269.0 / Mon Apr 28 2025 09:00:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.268.0 / Fri Apr 25 2025 10:27:54 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.267.0 / Fri Apr 25 2025 01:13:54 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.266.0 / Thu Apr 24 2025 23:50:49 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.265.0 / Thu Apr 24 2025 16:37:23 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator
- Feat: WEB XXX - Add / Update Widgets to enable Award Page Builds

v1.264.0 / Tue Apr 22 2025 15:49:20 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.263.0 / Tue Apr 22 2025 15:19:38 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.262.0 / Tue Apr 22 2025 11:37:06 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.261.0 / Tue Apr 22 2025 11:09:28 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.260.0 / Tue Apr 22 2025 07:57:46 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.259.0 / Tue Apr 15 2025 15:51:04 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.258.0 / Tue Apr 15 2025 15:10:37 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.257.0 / Tue Apr 15 2025 14:50:13 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.256.0 / Tue Apr 15 2025 13:22:51 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.255.0 / Tue Apr 15 2025 10:16:41 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.254.0 / Tue Apr 15 2025 09:54:16 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.253.0 / Mon Apr 14 2025 13:41:52 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.252.0 / Mon Apr 14 2025 11:28:27 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets
- Feat: Agenda navigator

v1.251.0 / Mon Apr 14 2025 08:42:37 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets

v1.250.0 / Fri Apr 11 2025 13:22:35 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Fix: Adds option to increase image size on sponsor strip
- Feat: WEB XXX - Adds Event Presets

v1.249.0 / Fri Apr 11 2025 10:34:00 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Advert unit offscreen on smaller viewport
- Feat: WEB XXX - Adds Event Presets

v1.248.0 / Tue Apr 08 2025 23:48:06 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Adds Event Presets

v1.247.0 / Tue Apr 08 2025 23:39:32 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Adds Event Presets

v1.246.0 / Tue Apr 08 2025 13:36:30 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Fix: SAML - Attempts to fix SAML Login not working 1st time
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.245.0 / Tue Apr 08 2025 12:53:44 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Fix: SAML - Attempts to fix SAML Login not working 1st time
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.244.0 / Tue Apr 08 2025 10:14:52 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Fix: SAML - Attempts to fix SAML Login not working 1st time
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.243.0 / Mon Apr 07 2025 22:17:08 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Fix: SAML - Attempts to fix SAML Login not working 1st time
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.242.0 / Mon Apr 07 2025 19:46:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.241.0 / Mon Apr 07 2025 18:56:27 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.240.0 / Mon Apr 07 2025 18:37:02 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry
- Feat: WEB XXX - Adds event page response checker (#augment-code)

v1.239.0 / Mon Apr 07 2025 18:06:11 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry

v1.238.0 / Mon Apr 07 2025 17:00:17 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip
- Feat: Sentry

v1.237.0 / Mon Apr 07 2025 13:16:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip

v1.236.0 / Mon Apr 07 2025 11:15:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip

v1.235.0 / Mon Apr 07 2025 10:55:22 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions
- Feat: WEB-XXX Event sponser strip

v1.234.0 / Thu Apr 03 2025 19:39:09 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration
- Feat: WEB XXX - Adds display date filter to CMS Suggestions

v1.233.0 / Thu Apr 03 2025 17:18:47 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration

v1.232.0 / Thu Apr 03 2025 17:02:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix WEB-XXX - Handles event entity nulls correctly
- Feat: WEB XXX - Open router integration

v1.231.0 / Thu Apr 03 2025 15:39:31 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Open router integration

v1.230.0 / Thu Apr 03 2025 11:30:03 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1088 - Adds Open Router Integration
- Feat: WEB XXX - Allows an Event to use another event's Navigation

v1.229.0 / Thu Apr 03 2025 11:09:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1088 - Adds Open Router Integration
- Feat: WEB XXX - Allows an Event to use another event's Navigation

v1.228.0 / Wed Apr 02 2025 17:45:51 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500
- Feat: WEB2025-1249 - Body syndicator adds instance select

v1.227.0 / Wed Apr 02 2025 17:20:16 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500
- Feat: WEB2025-1249 - Body syndicator adds instance select

v1.226.0 / Wed Apr 02 2025 16:13:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.225.0 / Wed Apr 02 2025 15:09:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.224.0 / Tue Apr 01 2025 14:57:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.223.0 / Tue Apr 01 2025 14:07:49 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.222.0 / Tue Apr 01 2025 13:34:37 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Uses formatting function which respects input timezone
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.221.0 / Tue Apr 01 2025 12:38:45 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.220.0 / Mon Mar 31 2025 15:32:01 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Replace clocks redirect trailing slash module with our own
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.219.0 / Mon Mar 31 2025 11:00:23 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.218.0 / Mon Mar 31 2025 10:25:38 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents bug where speakers with no next up agenda items cause 500

v1.217.0 / Thu Mar 27 2025 11:28:27 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB-XXX: Creates new slug updater tool
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo
- Feat: WEB2025-1369 - Create Event Sponsor Copy Layout Button
- Feat: Add new Salesforce field to Instance form

v1.216.0 / Wed Mar 26 2025 15:10:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB-XXX: Creates new slug updater tool
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo
- Feat: WEB2025-1369 - Create Event Sponsor Copy Layout Button

v1.215.0 / Wed Mar 26 2025 14:41:26 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB-XXX: Creates new slug updater tool
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo
- Feat: WEB2025-1369 - Create Event Sponsor Copy Layout Button

v1.214.0 / Tue Mar 25 2025 23:04:05 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo
- Feat: WEB2025-1369 - Create Event Sponsor Copy Layout Button

v1.213.0 / Tue Mar 25 2025 22:10:37 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo

v1.212.0 / Tue Mar 25 2025 21:04:47 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo

v1.211.0 / Tue Mar 25 2025 20:53:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo

v1.210.0 / Tue Mar 25 2025 20:21:21 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1358: Corrects locale across widgets
- Fix: WEB2025-1361 - Prevents Latest Magazine Cover not pulling through to Footer
- Feat: WEB2025-1359 - Reinstates speaker nextUp + company logo

v1.209.0 / Tue Mar 25 2025 08:40:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation
- Fix: WEB XXX - Adds OG image for Event Sections
- Fix: Removes italic on em tag in Outline heading
- Feat: WEB2025-1355 - Adds meta details in event
- Feat: WEB XXX - Changes name of "Uber Search" to "Link Search"
- Feat: WEB2025-1356 - Adds transition effects to buttons

v1.208.0 / Mon Mar 24 2025 17:33:48 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation
- Fix: WEB XXX - Adds OG image for Event Sections
- Feat: WEB2025-1355 - Adds meta details in event
- Feat: WEB XXX - Changes name of "Uber Search" to "Link Search"
- Feat: WEB2025-1356 - Adds transition effects to buttons

v1.207.0 / Thu Mar 20 2025 17:40:14 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation
- Fix: WEB XXX - Adds OG image for Event Sections
- Feat: WEB2025-1355 - Adds meta details in event
- Feat: WEB XXX - Changes name of "Uber Search" to "Link Search"

v1.206.0 / Thu Mar 20 2025 17:32:02 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation
- Fix: WEB XXX - Adds OG image for Event Sections
- Feat: WEB2025-1355 - Adds meta details in event

v1.205.7 / Thu Mar 20 2025 15:02:04 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation
- Fix: WEB XXX - Adds OG image for Event Sections

v1.205.6 / Wed Mar 19 2025 20:22:34 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure
- Fix: WEB XXX - Prevents external link not working in Event Navigation

v1.205.5 / Wed Mar 19 2025 19:02:00 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure

v1.205.4 / Wed Mar 19 2025 17:42:39 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure

v1.205.3 / Wed Mar 19 2025 17:26:54 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure

v1.205.2 / Wed Mar 19 2025 16:50:33 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure

v1.205.1 / Wed Mar 19 2025 15:56:48 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1351 - Changes Event Video URL Structure

v1.205.0 / Wed Mar 19 2025 15:22:09 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB-XXX - Event Content Block Minor Improvements
- Feat: WEB2025-1350 - Adds Save and Preview button to event layout forms
- Feat: WEB2025-1350 - Visible and scheduled Event pages

v1.204.0 / Wed Mar 19 2025 14:09:28 GMT+0000 (Coordinated Universal Time)
====================================
- WEB 424 - Ad Unit Renaming #214 (Isolated) + Reloading
- Feat: WEB2025-1353: Adds Magazine links to Footer to boost SEO

v1.203.0 / Wed Mar 19 2025 13:50:33 GMT+0000 (Coordinated Universal Time)
====================================
- WEB 424 - Ad Unit Renaming #214 (Isolated) + Reloading
- Feat: WEB2025-1353: Adds Magazine links to Footer to boost SEO

v1.202.0 / Wed Mar 19 2025 12:20:11 GMT+0000 (Coordinated Universal Time)
====================================
- WEB 424 - Ad Unit Renaming #214 (Isolated) + Reloading

v1.201.0 / Tue Mar 18 2025 09:02:11 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Fix: WEB2025-1339 - Prevents load more button not working
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles
- Feat: Header UX amends
- Feat: Magaziner tilt

v1.200.0 / Mon Mar 17 2025 18:28:51 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Fix: WEB2025-1339 - Prevents load more button not working
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles
- Feat: Header UX amends

v1.199.0 / Mon Mar 17 2025 17:20:04 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Fix: WEB2025-1339 - Prevents load more button not working
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles
- Feat: Header UX amends

v1.198.0 / Mon Mar 17 2025 16:20:00 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles
- Feat: Header UX amends

v1.197.0 / Mon Mar 17 2025 12:24:08 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles
- Feat: Header UX amends

v1.195.0 / Sat Mar 15 2025 07:38:23 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles

v1.194.0 / Fri Mar 14 2025 16:41:11 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB2025-1329 -  Fix anchors with no content
- Feat: WEB2025-1321 - Adds FeaturedMagazine & MagazineGrid widgets + featured magazines and magazine categories
- Feat: WEB2025-1330 - Correctly handles secondary event section canonical URLs
- Feat: WEB2025-109 -  Add captions to videos on articles

v1.193.1 / Fri Mar 14 2025 11:54:35 GMT+0000 (Coordinated Universal Time)
====================================
- FIX: WEB-XXX: Removes hard coded 'Sponsor' from label

v1.193.0 / Fri Mar 07 2025 16:35:30 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1137 - Temporoarily hides event video card subtitle (eventName)
- Feat: WEB2025-1138 - Adds visibility option to Agenda Item
- Feat: #WEB2025-1319 - Adds an magazine override select

v1.192.0 / Fri Mar 07 2025 16:15:53 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1137 - Temporoarily hides event video card subtitle (eventName)
- Feat: WEB2025-1138 - Adds visibility option to Agenda Item
- Feat: #WEB2025-1319 - Adds an magazine override select

v1.191.0 / Fri Mar 07 2025 15:53:34 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1137 - Temporoarily hides event video card subtitle (eventName)
- Feat: WEB2025-1138 - Adds visibility option to Agenda Item
- Feat: #WEB2025-1319 - Adds an magazine override select

v1.190.0 / Fri Mar 07 2025 14:57:44 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB2025-1137 - Temporoarily hides event video card subtitle (eventName)

v1.189.0 / Wed Mar 05 2025 18:01:39 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Correctly identifies environment on advert targeting object
- Fix: Prevents Article Payload from exceeding limit by fixing asset <-> article.image tag syncing
- feat: devcontainers & staging domain changes
- WEB 424 - Ad Unit Renaming #214 (Isolated)
- Feat: Adds filters + refactors frontend ai link assistant code

v1.188.0 / Wed Mar 05 2025 17:50:16 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Correctly identifies environment on advert targeting object
- Fix: Prevents Article Payload from exceeding limit by fixing asset <-> article.image tag syncing
- feat: devcontainers & staging domain changes
- WEB 424 - Ad Unit Renaming #214 (Isolated)
- Feat: Adds filters + refactors frontend ai link assistant code

v1.187.0 / Mon Mar 03 2025 12:20:30 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Correctly identifies environment on advert targeting object
- Fix: Prevents Article Payload from exceeding limit by fixing asset <-> article.image tag syncing
- feat: devcontainers & staging domain changes
- WEB 424 - Ad Unit Renaming #214 (Isolated)
- Feat: Adds filters + refactors frontend ai link assistant code

v1.186.0 / Fri Feb 28 2025 12:19:23 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Correctly identifies environment on advert targeting object
- feat: devcontainers & staging domain changes
- WEB 424 - Ad Unit Renaming #214 (Isolated)

v1.183.0 / Thu Feb 27 2025 11:29:18 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Correctly identifies environment on advert targeting object
- feat: devcontainers & staging domain changes
- WEB 424 - Ad Unit Renaming #214 (Isolated)

v1.182.0 / Tue Feb 25 2025 18:51:58 GMT+0000 (Coordinated Universal Time)
====================================
- feat: devcontainers & staging domain changes

v1.181.0 / Thu Feb 20 2025 14:32:36 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Prevents long stage names looking weird for in person
- Fix: A couple of minor event fixes
- Feat: Adds extra functionality to button actions

v1.180.0 / Thu Feb 20 2025 12:53:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Prevents long stage names looking weird for in person
- Fix: A couple of minor event fixes
- Feat: Adds extra functionality to button actions

v1.179.0 / Fri Feb 14 2025 09:04:08 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Prevents long stage names looking weird for in person
- Feat: Adds extra functionality to button actions

v1.178.0 / Wed Feb 12 2025 19:32:37 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adds extra functionality to button actions

v1.177.0 / Tue Feb 11 2025 19:28:03 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adds extra functionality to button actions

v1.176.0 / Tue Feb 11 2025 18:55:28 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: Adds extra functionality to button actions

v1.175.0 / Tue Feb 11 2025 16:31:00 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Corrects register action behaviour + reanbles CMS button group for Event Content Block
- Feat: WidgetArea widget id - enables scrolling directly to widgets #277
- Feat: Adds stage label to Event Content Grid
- Feat: Event Video List - Adds event column to list + event filter
- Feat: Adds title to event widgets in the CMS
- Feat: Event Sponsor Booth can be hidden

v1.174.0 / Tue Feb 11 2025 15:54:39 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Corrects register action behaviour + reanbles CMS button group for Event Content Block
- Feat: WidgetArea widget id - enables scrolling directly to widgets #277
- Feat: Adds stage label to Event Content Grid
- Feat: Event Video List - Adds event column to list + event filter
- Feat: Adds title to event widgets in the CMS

v1.173.0 / Tue Feb 11 2025 15:34:12 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: Corrects register action behaviour + reanbles CMS button group for Event Content Block
- Feat: WidgetArea widget id - enables scrolling directly to widgets #277
- Feat: Adds stage label to Event Content Grid
- Feat: Event Video List - Adds event column to list + event filter
- Feat: Adds title to event widgets in the CMS

v1.169.0 / Wed Jan 15 2025 15:10:22 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - 13 jan 2025 article slug hotfix
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication
- Feat: WEB XXX - Adds themed bullet point to image-split-hero bullet points
- Feat: WEB XXX - Week beg. 13 Jan minor event tweaks

v1.168.0 / Tue Jan 14 2025 21:24:10 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - 13 jan 2025 article slug hotfix
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication
- Feat: WEB XXX - Adds themed bullet point to image-split-hero bullet points

v1.167.0 / Mon Jan 13 2025 16:02:18 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - 13 jan 2025 article slug hotfix
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication

v1.166.0 / Thu Jan 09 2025 11:33:13 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication

v1.165.0 / Thu Jan 09 2025 01:08:28 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication

v1.164.0 / Thu Jan 09 2025 00:36:12 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: XXX - Creates Article Internal and Back Link Suggestions
- feat(events): adds event layout duplication

v1.163.0 / Tue Jan 07 2025 14:14:08 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Should prevent duplicate articles being uploaded to the CMS
- Fix: WEB XXX - Prevents event on-demand player placeholder from showing
- Fix: WEB XXX - Various UX changes
- feat(forms): FormCrafts integration replacing WebToLead

v1.139.0 / Fri Nov 29 2024 08:26:26 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents CLS in Hero widgets
- Fix: WEB XXX - Makes various Navigational & Link changes 
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles
- Feat: WEB XXX - Updates Event Stats styling

v1.138.0 / Fri Nov 29 2024 01:32:44 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents CLS in Hero widgets
- Fix: WEB XXX - Makes various Navigational & Link changes 
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles
- Feat: WEB XXX - Updates Event Stats styling

v1.137.0 / Fri Nov 29 2024 01:07:13 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents CLS in Hero widgets
- Fix: WEB XXX - Makes various Navigational & Link changes 
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles
- Feat: WEB XXX - Updates Event Stats styling

v1.136.0 / Thu Nov 28 2024 23:51:44 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents CLS in Hero widgets
- Fix: WEB XXX - Makes various Navigational & Link changes 
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles
- Feat: WEB XXX - Updates Event Stats styling

v1.135.0 / Thu Nov 28 2024 16:55:36 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles
- Feat: WEB XXX - Updates Event Stats styling

v1.134.0 / Thu Nov 28 2024 16:19:16 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles

v1.133.0 / Thu Nov 28 2024 00:22:31 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles

v1.132.0 / Thu Nov 28 2024 00:12:57 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles

v1.131.0 / Wed Nov 27 2024 23:15:40 GMT+0000 (Coordinated Universal Time)
====================================
- Feat: WEB XXX - Updates styling
- Fix: WEB XXX - Migrates event related entities to be compatible with new event subcollections
- Feat: WEB XXX - Allows highlight component to be different sizes
- Feat: WEB XXX - Finished up Agenda Modal popup + Event Content Grid polish
- Feat: WEB XXX - Adds Martin's Icon pack + integrates into event widgets
- Feat: WEB XXX - Adds default piano register action
- Feat: WEB XXX - Adds Event Article Grid widget & adds event category to articles

v1.130.1 / Wed Nov 13 2024 19:27:58 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XX - Prevents Agenda crash due to maintaining old property in graphql query

v1.130.0 / Wed Nov 13 2024 18:54:45 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS
- Feat: WEB XXX - Adds brand colors to event & event umbrella
- Fix: WEB XXX - Refactors event subcollections

v1.129.0 / Wed Nov 13 2024 17:55:11 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS
- Feat: WEB XXX - Adds brand colors to event & event umbrella
- Fix: WEB XXX - Refactors event subcollections

v1.128.0 / Wed Nov 13 2024 15:59:42 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS
- Feat: WEB XXX - Adds brand colors to event & event umbrella
- Fix: WEB XXX - Refactors event subcollections

v1.127.0 / Wed Nov 13 2024 15:33:21 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS
- Feat: WEB XXX - Adds brand colors to event & event umbrella
- Fix: WEB XXX - Refactors event subcollections

v1.126.0 / Wed Nov 13 2024 02:12:34 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS
- Feat: WEB XXX - Adds brand colors to event & event umbrella
- Fix: WEB XXX - Refactors event subcollections

v1.125.0 / Tue Nov 12 2024 18:38:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS

v1.124.0 / Tue Nov 12 2024 14:57:07 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch
- Fix: WEB XXX - Allows all event sub content types to be sortable within the CMS

v1.123.2 / Fri Nov 08 2024 09:54:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Recreates Event Agenda widget from scratch

v1.123.1 / Thu Nov 07 2024 13:27:41 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents MagazineCTA Widget being undefined from breaking Company Profile widget

v1.123.0 / Wed Nov 06 2024 17:35:25 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents 500 Error on Event home page
- Fix: WEB XXX - Allows Event Testimonials to be from non-speakers + adds speakers company names across widgtes
- Feat: WEB XXX - Revert Most Recent to Most Relevant Search
- Fix: WEB XXX - Enables Scope3 Favicon to show
- Feat: WEB XXX - Adds a video background option to the Event Hero widget
- Feat: WEB XXX - Allows logo to be disabled in navigation
- Feat: WEB XXX - Adds functionality to action on ButtonGroup

v1.122.0 / Wed Nov 06 2024 10:27:01 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents 500 Error on Event home page
- Fix: WEB XXX - Allows Event Testimonials to be from non-speakers + adds speakers company names across widgtes
- Feat: WEB XXX - Revert Most Recent to Most Relevant Search
- Feat: WEB XXX - Adds a video background option to the Event Hero widget
- Feat: WEB XXX - Allows logo to be disabled in navigation

v1.121.0 / Wed Nov 06 2024 00:42:51 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents 500 Error on Event home page
- Fix: WEB XXX - Allows Event Testimonials to be from non-speakers + adds speakers company names across widgtes
- Feat: WEB XXX - Revert Most Recent to Most Relevant Search
- Feat: WEB XXX - Adds a video background option to the Event Hero widget
- Feat: WEB XXX - Allows logo to be disabled in navigation

v1.120.0 / Tue Nov 05 2024 23:45:55 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents 500 Error on Event home page
- Fix: WEB XXX - Allows Event Testimonials to be from non-speakers + adds speakers company names across widgtes
- Feat: WEB XXX - Revert Most Recent to Most Relevant Search
- Feat: WEB XXX - Adds a video background option to the Event Hero widget

v1.119.1 / Tue Nov 05 2024 11:23:35 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Prevents 500 Error on Event home page

v1.119.0 / Tue Nov 05 2024 01:14:54 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX: Prevents the Blockquote widget from displaying as empty
- Fix: WEB XXX - Enables article layout slugs to the same as other existing layout slugs (such as /about)
- Fix: WEB XXX - Prevents Highlight widget from breaking if h1/h2/h3/h4 tags being used.
- Fix: WEB XXX - Prevents undefined eventLayout in the case of deletion from crashing
- Fix: WEB XXX - Updates magazine-select pageSize to include all magazine issues
- Fix: WEB XXX - Swaps some inputs for textarea descriptions on event widgets
- Fix: WEB-XXX: Adds a fallback limit on articleGrid items
- Feat: WEB XXX: Adds preview button to event layout list items

v1.118.1 / Mon Nov 04 2024 15:21:30 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB-XXX - Prevents when app crashes from spamming articleIds undefined error...

v1.118.0 / Thu Oct 24 2024 10:17:20 GMT+0000 (Coordinated Universal Time)
====================================
- feat(events): restyles event content grid items

v1.117.0 / Tue Oct 15 2024 17:14:31 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Updates yarnlock file
- Fix: WEB XXX - Allows Live player Stage Select to be used when a stage has finished
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List
- Feat: WEB XXX - Prevents Event Navigation from being too short

v1.116.0 / Mon Oct 14 2024 20:01:35 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Updates yarnlock file
- Fix: WEB XXX - Allows Live player Stage Select to be used when a stage has finished
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List
- Feat: WEB XXX - Prevents Event Navigation from being too short

v1.115.0 / Mon Oct 14 2024 15:56:59 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Updates yarnlock file
- Fix: WEB XXX - Allows Live player Stage Select to be used when a stage has finished
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List
- Feat: WEB XXX - Prevents Event Navigation from being too short

v1.114.0 / Mon Oct 14 2024 15:35:19 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Updates yarnlock file
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List
- Feat: WEB XXX - Prevents Event Navigation from being too short

v1.113.0 / Mon Oct 14 2024 15:05:31 GMT+0000 (Coordinated Universal Time)
====================================
- Fix: WEB XXX - Updates yarnlock file
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List
- Feat: WEB XXX - Prevents Event Navigation from being too short

v1.112.0 / Mon Oct 14 2024 14:14:09 GMT+0000 (Coordinated Universal Time)
====================================
- Feat/event agenda admin UI
- Feat: WEB XXX - Changes UI for the Event Agenda List

v1.111.0 / Fri Oct 11 2024 20:00:02 GMT+0000 (Coordinated Universal Time)
====================================
- Feature: WEB 301 - Implements SAML login for CMS access

# v1.110.1 / Fri Oct 11 2024 19:48:48 GMT+0000 (Coordinated Universal Time)

- Fix: WEB XXX - Prevents CLS of site navigation

# v1.110.0 / Fri Oct 11 2024 19:35:18 GMT+0000 (Coordinated Universal Time)

- Feature: WEB 301 - Implements SAML login for CMS access

# v1.109.0 / Fri Oct 11 2024 19:20:41 GMT+0000 (Coordinated Universal Time)

- Feature: WEB 301 - Implements SAML login for CMS access

# v1.107.0 / Fri Oct 11 2024 13:55:08 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.106.0 / Fri Oct 11 2024 09:17:51 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.105.0 / Thu Oct 10 2024 15:54:53 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.104.0 / Thu Oct 10 2024 15:43:36 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.103.0 / Thu Oct 10 2024 15:06:04 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.102.0 / Tue Oct 08 2024 11:52:00 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.101.0 / Fri Oct 04 2024 09:37:48 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.100.0 / Fri Oct 04 2024 09:08:03 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.99.0 / Tue Oct 01 2024 15:55:10 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.98.0 / Tue Oct 01 2024 12:04:46 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.97.0 / Mon Sep 30 2024 09:01:40 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.96.0 / Sat Sep 28 2024 00:52:09 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.95.0 / Sat Sep 28 2024 00:40:50 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.94.0 / Sat Sep 28 2024 00:11:07 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.93.0 / Fri Sep 27 2024 23:46:52 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.92.0 / Fri Sep 27 2024 23:03:18 GMT+0000 (Coordinated Universal Time)

- BIG Feature: Events Platform

# v1.91.1 / Fri Sep 27 2024 21:55:48 GMT+0000 (Coordinated Universal Time)

- Fix: WEB XXX - Adds script to cleanup many ISSU magazines
- Fix: WEB 19 - Adds script to cleanup many ISSU magazines

# v1.91.0 / Wed Sep 25 2024 23:52:43 GMT+0000 (Coordinated Universal Time)

- Feat: WEB XXX - Changes layout for company page + adds LatestContent component

# v1.90.1 / Wed Sep 25 2024 23:25:49 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 448 - Prevents article lists from showing articles with the wrong category

# v1.90.0 / Wed Sep 25 2024 19:37:03 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 452 - Prevents overflow on Mobile / iPad devices
- Fix: WEB 451 - Sets list item font size to match the rest of the text body
- Feat: WEB 455 - Prevents Magazine Description from exceeding 300 characters

# v1.89.0 / Wed Sep 25 2024 19:01:08 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 452 - Prevents overflow on Mobile / iPad devices
- Fix: WEB 451 - Sets list item font size to match the rest of the text body
- Feat: WEB 455 - Prevents Magazine Description from exceeding 300 characters

# v1.88.0 / Wed Sep 25 2024 18:58:10 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 452 - Prevents overflow on Mobile / iPad devices
- Fix: WEB 451 - Sets list item font size to match the rest of the text body
- Feat: WEB 455 - Prevents Magazine Description from exceeding 300 characters

# v1.87.0 / Wed Sep 25 2024 18:30:18 GMT+0000 (Coordinated Universal Time)

- Feat: WEB XXX - Latest magazine variation

# v1.86.2 / Wed Sep 25 2024 13:06:05 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 109 - Prevents article from being created multiple times

# v1.86.1 / Wed Sep 04 2024 11:22:40 GMT+0000 (Coordinated Universal Time)

- fix(apple-advertise-form): prevents incorrect interval declaration br…

# v1.86.0 / Tue Sep 03 2024 10:38:10 GMT+0000 (Coordinated Universal Time)

- Allows home page layouts to be duplicated.
- Themes for Scope 3 and ClimateTech

# v1.85.0 / Tue Aug 27 2024 10:46:59 GMT+0000 (Coordinated Universal Time)

- WEB-300 - Improve CMS UI for Duplicating Sections

# v1.84.0 / Fri Aug 23 2024 15:32:01 GMT+0000 (Coordinated Universal Time)

- Feat: WEB 401: Redesigns The Site's Navigation

# v1.83.0 / Fri Aug 23 2024 14:58:39 GMT+0000 (Coordinated Universal Time)

- feat(article): adds basic syndicate article body button

# v1.82.0 / Fri Aug 23 2024 14:30:57 GMT+0000 (Coordinated Universal Time)

- feat(article): adds basic syndicate article body button

# v1.81.0 / Fri Aug 23 2024 10:55:06 GMT+0000 (Coordinated Universal Time)

- feat(article): adds basic syndicate article body button

# v1.80.0 / Fri Aug 23 2024 08:47:39 GMT+0000 (Coordinated Universal Time)

- Feat: WEB 439 - Adds Fetch Apollo Data to Company field + small related bug fixes

# v1.79.0 / Thu Aug 22 2024 15:11:20 GMT+0000 (Coordinated Universal Time)

- feat(domains): adds new domains to northflank config

# v1.78.0 / Wed Aug 07 2024 15:47:39 GMT+0000 (Coordinated Universal Time)

- Updates Food & Drink icons to red and yellow.

# v1.77.0 / Wed Aug 07 2024 15:24:47 GMT+0000 (Coordinated Universal Time)

- Fix: WEB [134|302|328|372] - Various article QoL changes
- Fix: WEB 317 - Updates article image's tags within the assets panel on article save
- Fix: WEB 120 - Fixes Simple Article Grid Deduplication
- Feat: WEB-416 - Reduces size of standard body font

# v1.76.0 / Wed Aug 07 2024 15:03:44 GMT+0000 (Coordinated Universal Time)

- Updates Food & Drink icons to red and yellow.

# v1.75.0 / Wed Aug 07 2024 11:03:49 GMT+0000 (Coordinated Universal Time)

- Updates Food & Drink icons to red and yellow.

# v1.74.0 / Mon Jul 29 2024 11:42:18 GMT+0000 (Coordinated Universal Time)

- Fix: WEB [134|302|328|372] - Various article QoL changes
- Fix: WEB 317 - Updates article image's tags within the assets panel on article save
- Fix: WEB 120 - Fixes Simple Article Grid Deduplication
- Feat: WEB-416 - Reduces size of standard body font

# v1.73.0 / Tue Jul 23 2024 16:56:07 GMT+0000 (Coordinated Universal Time)

- Fix WEB 396 - Updates 5G & IoT categories articles
- Fix: WEB 279 - Prevents repositioning layouts in layout editor causing row deletion bug
- WEB-388 - Removes Icons and links to Corporate Site from the footer.
- WEB-391 - Removes "Sign up to view" from CMS UI Article Template

# v1.72.5 / Tue Jul 23 2024 10:38:49 GMT+0000 (Coordinated Universal Time)

- fix(sitemaps): prevents sitemap documents from trying to generate duplicates

# v1.72.4 / Tue Jul 23 2024 09:58:31 GMT+0000 (Coordinated Universal Time)

- fix(sitemaps): prevents sitemap documents from trying to generate duplicates

# v1.72.3 / Mon Jul 22 2024 16:56:17 GMT+0000 (Coordinated Universal Time)

- fix(sitemaps): prevents sitemap documents from trying to generate duplicates

# v1.72.2 / Wed Jul 03 2024 16:43:54 GMT+0000 (Coordinated Universal Time)

- Fix: Prevents Magazine Viewer chopping bottom off in full screen view

# v1.72.1 / Wed Jul 03 2024 16:38:38 GMT+0000 (Coordinated Universal Time)

- Fix: Prevents Magazine Viewer chopping bottom off in full screen view

# v1.72.0 / Tue Jun 18 2024 21:29:57 GMT+0000 (Coordinated Universal Time)

- Fix: WEB XXX - \_document change - prevents twitter snippet from breaking client side js
- Feature: WEB 86 - Introduces article compression of mobile images

# v1.71.2 / Tue Jun 18 2024 13:29:48 GMT+0000 (Coordinated Universal Time)

- Chore: Removes script logging causing failing CI checks

# v1.71.1 / Tue Jun 18 2024 11:20:09 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 377 - Reruns de-duplication script now purges duplicates

# v1.71.0 / Mon Jun 17 2024 17:15:43 GMT+0000 (Coordinated Universal Time)

- Feature: March8 - Optimises march8's font delivery to allow for more accurate testing metrics

# v1.70.0 / Mon Jun 17 2024 17:05:23 GMT+0000 (Coordinated Universal Time)

- Feature: March8 - Optimises march8's font delivery to allow for more accurate testing metrics
  v1.69.0 / Mon Jun 17 2024 14:53:27 GMT+0000 (Coordinated Universal Time)
  ====================================
- Fix WEB 365: Introduces new lazy loading for images
- Feature: WEB 376 - Defers script in hopes of increasing FCP & LCP
- Feature: LCP Optimisations #1
- Feature: WEB 359 - Optimises Youtube Embed

# v1.68.0 / Mon Jun 17 2024 14:26:31 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 367 - Prevents Large Advert from causing CLS
- Fix: WEB 365 - Prevents Magazine Issue Viewer CLS
- Feature: WEB 316 - Adds 60 char limit to asset captions
- Feature: WEB 359 - Adds toggle to instances to prevent ads from loading on mobile

# v1.58.0 / Tue Jun 11 2024 13:46:22 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 370 - Prevents unknown resource from breaking api
- Feature: WEB 330 - Sets 'Most Recent' as default search filter
- Feature: WEB 331 - Fixes 'mostPopular30Days' to only choose from articles displayed within the last 30 days
- Feature: WEB-339 - Old Articles - Set Author to "Bizclik Admin"
- Feature: WEB 356 - Add Horizontal Line to CKEditor

# v1.56.1 / Fri May 17 2024 14:08:55 GMT+0000 (Coordinated Universal Time)

- URGENT Fix: Adds Supply Chain Digital manifest + forces redeployment

# v1.56.0 / Thu May 09 2024 10:47:48 GMT+0000 (Coordinated Universal Time)

- Feature: 3rd Party - Implements wildcard redirect capability
- Feature: Web 80 - update article schema

# v1.55.0 / Tue May 07 2024 13:09:12 GMT+0000 (Coordinated Universal Time)

- Feature: WEB-315 - Enables viewing of the article publisher within the article export
- Fix: Web-320 - AI Magazine, "Atos Edge Transformations are Driving Business Value" - View Report button is 404ing
- Feature: Web-294 - Change Twitter to "X" in the footer
- Feature: Web-199 - Bizclik Site Magazines Reports Feeds

# v1.54.0 / Mon Apr 15 2024 09:42:30 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 313 - Allows ad slot targeting to be updated when navigating between pages
- Feature: WEB 312 - Enables videos to be linkable via the URL hash

# v1.53.0 / Thu Mar 28 2024 08:49:04 GMT+0000 (Coordinated Universal Time)

- Fix: WEB 307 - Prevents 2 articles with the same slug being saved under same section
- Feature: WEB 299 - Introduces better tracking data from article publishing
- Feature: WEB 283 - Logo Update
- Feature: WEB-311 - Add Favicon for Food Magazine
- Patch: WEB 281 - Corrects "Visit Company" button links found on Company Profile pages

# v1.52.0 / Wed Mar 06 2024 12:05:20 GMT+0000 (Coordinated Universal Time)

- Feat/combine duplicate companies

# v1.51.0 / Wed Feb 28 2024 15:00:05 GMT+0000 (Coordinated Universal Time)

- Web 214 - Updating Favicons
- Web 219 - Theme Colour Fix

# v1.50.0 / Mon Feb 19 2024 12:02:30 GMT+0000 (Coordinated Universal Time)

- Add e-edition form to Whitepaper article content type, and "view Whitepaper" button to Article Template
- Feature/web 203 supply chain and others robotstxt looks odd
- Adds site colours, logo URLs and article content type to dataLayer.

# v1.49.0 / Wed Feb 14 2024 15:44:15 GMT+0000 (Coordinated Universal Time)

- feat(category): adds instance select next to List categories select
- Feat/header navigation reload

# v1.48.0 / Wed Jan 17 2024 09:53:05 GMT+0000 (Coordinated Universal Time)

- Bug 17 - Datalayer not populated on initial page load or refresh

# v1.47.0 / Mon Dec 18 2023 13:31:09 GMT+0000 (Coordinated Universal Time)

- Feature/robots field in cms dashboard
- feat(site: BrandContact): updates salesforce form fields
- Reinstates "advanced" tab on CKEditor for full control of in-article …
- Feature/piano login register

# v1.46.0 / Tue Nov 28 2023 17:20:42 GMT+0000 (Coordinated Universal Time)

- feat(cms:article): speed improvements

# v1.45.0 / Tue Nov 28 2023 17:10:11 GMT+0000 (Coordinated Universal Time)

- feat(cms:article): speed improvements

# v1.44.0 / Tue Nov 28 2023 16:16:40 GMT+0000 (Coordinated Universal Time)

- feat(cms:article): speed improvements

# v0.0.1 / Mon Nov 20 2023 16:39:29 GMT+0000 (Coordinated Universal Time)

- fix(cms:api): bumps resources as CMS is being reported to be slow

# v1.43.0 / Wed Sep 27 2023 13:10:35 GMT+0000 (Coordinated Universal Time)

- Add HTML widget to Article Layout
- Feature/piano content tags
- feat(GlobalNavigation): hides community picker
- Changed the string Search to Find

# v1.42.0 / Thu Sep 14 2023 13:45:08 GMT+0000 (Coordinated Universal Time)

- Feature/site header styling changes
- Feat/enlarge header logo

# v1.41.1 / Mon Aug 14 2023 15:43:21 GMT+0000 (Coordinated Universal Time)

- fix(articles): Fixes layout duplication

# v1.41.0 / Mon Aug 07 2023 10:10:57 GMT+0000 (Coordinated Universal Time)

- feat(magazines): Adds joomag option to CMS

# v1.40.0 / Wed Jul 12 2023 16:17:46 GMT+0000 (Coordinated Universal Time)

- fix(darkroom): Darkroom staging to use production
- feat(darkroom): Updates darkroom as staging has been removed
- feat(ad-slots): Add page id key to targeting config

# v1.39.1 / Thu May 18 2023 12:42:56 GMT+0000 (UTC)

- fix(images): Fixes images when they don't exist for other pages
- bug(canonical): Only sets a canonical URL if it exists.

# v1.39.0 / Thu May 18 2023 11:37:58 GMT+0000 (UTC)

- feat: Share Image - add an ability to set a fallback social share image on instance level

# v1.38.1 / Thu May 18 2023 10:53:36 GMT+0000 (UTC)

- Data Centre Magazine captcha signup fail fix

# v1.38.0 / Thu May 11 2023 11:09:13 GMT+0000 (UTC)

- fix(sharing): Updates sharing widget to make use of react-share
- fix(opengraph): Ensure section og images are the correct size and crop
- feat(footer): Updates footer wording
- feat(articles): If an article is published or has been so lock the slug field
- feat(news-ticker): Adds option to hide the ticker in instances
- Feat(cms): Updates the sorting for select fields

# v1.37.2 / Tue May 02 2023 09:42:42 GMT+0000 (UTC)

- fix(500): Adds a optional chaining operator to authors

# v1.37.1 / Wed Apr 26 2023 10:28:15 GMT+0000 (UTC)

- fix(brochure): resource does not exist on brochure pages

# v1.37.0 / Wed Apr 26 2023 08:46:28 GMT+0000 (UTC)

- feat(ads): Sets page level value-key targeting

# v1.36.1 / Mon Mar 27 2023 12:07:04 GMT+0000 (UTC)

- fix(site): Ignore non-error promise rejections

# v1.36.0 / Thu Mar 23 2023 15:51:17 GMT+0000 (UTC)

- feat(support): Adds script to export all duplicate companies to csv
- feat(robots): Disallow queries in robots file
- feat: Ignore Microsoft SafeLink Crawler errors
- feat(admin): Adds manage button to dropdowns in sections
- feat(articles): Adds a nofollow to all tags with links

# v1.35.7 / Thu Mar 23 2023 09:08:45 GMT+0000 (UTC)

- fix(previews): Previews 404ing

# v1.35.6 / Tue Mar 21 2023 15:41:15 GMT+0000 (UTC)

- bug(brochures): Remove query params from brochure pages

# v1.35.4 / Tue Mar 14 2023 11:11:50 GMT+0000 (UTC)

- bug(meta): Removes query strings from URLS in canonicals

# v1.35.3 / Thu Mar 02 2023 13:45:08 GMT+0000 (UTC)

- Bug/newsletter signup modal

# v1.35.2 / Tue Feb 28 2023 16:48:07 GMT+0000 (UTC)

- fix(utility-navigation): Ensure nav closes when utility links are cli…

# v1.35.1 / Mon Feb 27 2023 10:35:36 GMT+0000 (UTC)

- fix(header-advert): Prevent CLS

# v1.35.0 / Wed Feb 22 2023 12:22:18 GMT+0000 (UTC)

- feat(admin): Implement the changelog viewer view
- Feature: makes image caption a mandatory field

# v1.34.0 / Tue Feb 21 2023 10:47:56 GMT+0000 (UTC)

- feat: Articles - add an ability to set a custom canonical URL on duplicate articles
- feat: Interviews - add 3 sub-types + fix sub-type field rendering bug
- feat(sentry): add DSN

# v1.33.1 / Wed Feb 15 2023 10:04:04 GMT+0000 (UTC)

- fix: Ensure Issuu reader loads - remove cookie auto blocker

# v1.33.0 / Mon Feb 13 2023 10:07:43 GMT+0000 (UTC)

- feat: Implement an ability to export article data to CSV
- feat: Integrate the cookie pro with the platform

# v1.32.0 / Thu Feb 09 2023 15:26:39 GMT+0000 (UTC)

- feat: Add no index directive to the error pages

# v1.31.0 / Mon Jan 30 2023 15:01:58 GMT+0000 (UTC)

- feat: Robots.txt - disallow /cdn-cgi/, /404, /search

# v1.30.1 / Thu Jan 26 2023 10:49:55 GMT+0000 (UTC)

- fix: Prevent articles throwing 500 error (due to missing context)

# v1.30.0 / Mon Jan 23 2023 14:05:29 GMT+0000 (UTC)

- Feature: hero image carousel
- Feature/holding page

# v1.29.0 / Tue Nov 15 2022 16:05:51 GMT+0000 (UTC)

- Feature: adds Google Structure Logo
- Feature: renames advertise page note field label
- Feature: Changes wording on Newsletter sign up widget

# v1.28.1 / Thu Nov 03 2022 10:35:48 GMT+0000 (UTC)

- Bug/newsletter barrier page

# v1.28.0 / Wed Oct 05 2022 13:16:56 GMT+0000 (UTC)

- Fixes B2B magazine display with 1-2 items
- feat(article: author): makes author field within an article mandatory
- feat(inline-images): adds destination input to allow inline-images to be hyperlinked

# v1.27.0 / Tue Sep 20 2022 07:50:24 GMT+0000 (UTC)

- feat(prose: anchor): fixes broken anchor functionality

# v1.26.0 / Wed Sep 14 2022 09:59:05 GMT+0000 (UTC)

- feat: live character counts

# v1.25.0 / Fri Sep 09 2022 08:28:25 GMT+0000 (UTC)

- feat: switch email from to clock.uk domain

# v1.24.0 / Tue Sep 06 2022 15:09:16 GMT+0000 (UTC)

- feat(Admin: Article): Automatically pull the video thumbnail as the main image when uploading a video

# v1.23.1 / Thu Sep 01 2022 10:34:59 GMT+0000 (UTC)

- fix(bmg-logos): Fix regression

# v1.23.0 / Thu Sep 01 2022 08:13:05 GMT+0000 (UTC)

- fix(Site: Header Advert): Removes header advert padding if ad slot is not published on site
- Updates BMG footer logos and display order

# v1.22.1 / Wed Aug 10 2022 15:51:40 GMT+0000 (UTC)

- fix(newsletter): Updated newsletter popup message

# v1.22.0 / Wed Aug 10 2022 10:48:02 GMT+0000 (UTC)

- feat(newsletter): Show modal if page changes
- feat(header): Add area in the header for adverts

# v1.21.0 / Tue Aug 02 2022 13:34:24 GMT+0000 (UTC)

- feat(site): Added options for header
- feat(site): Added and updated EV assets
- feat(site): Added Google Optimize
- feat(site:logos): Removed BMG studio logo

# v1.20.0 / Mon Jul 25 2022 13:15:57 GMT+0000 (UTC)

- feat(cache): Adds versionator to site

# v1.19.2 / Tue Jul 19 2022 15:07:11 GMT+0000 (UTC)

- Revert "fix(site:favicons): Updates Business Chief favicon names"

# v1.19.1 / Tue Jul 19 2022 13:18:25 GMT+0000 (UTC)

- Updates Business Chief favicon names

# v1.19.0 / Tue Jul 19 2022 08:08:33 GMT+0000 (UTC)

- fix(site:favicons): Updates Business Chief favicons

# v1.18.3 / Tue Jul 05 2022 11:07:07 GMT+0000 (UTC)

- fix(gpt): Remove duplicate advert loading code

# v1.18.2 / Wed Jun 29 2022 13:08:36 GMT+0000 (UTC)

- fix(Site: Brochure): Fixes missing full screen pop up

# v1.18.1 / Mon Jun 27 2022 15:14:26 GMT+0000 (UTC)

- fix(admin:administrator): Add first name, last name and email indexes

# v1.18.0 / Mon Jun 13 2022 12:56:58 GMT+0000 (UTC)

- fix(Site: Subscriber messaging): Now displays 'Already subscribed' message if user is already subscribed
- feat(Site: Advertise Form): Updates options for industry dropdown

# v1.17.0 / Mon Jun 13 2022 10:07:10 GMT+0000 (UTC)

- fix(brand-stats): Change background color
- fix(Site: About Us): Centres Brand Stats widget
- User sees a new brand feature carousel widget
- User sees new about us header widget
- chore(About Brand Widget): db script updating About Brand Widget

# v1.16.2 / Thu Jun 09 2022 08:29:18 GMT+0000 (UTC)

- fix(Admin: Article): Setting livedate now overrides the displaydate when publishing article
- fix(Site: Full Screen Pop up): full screen pop up now works when accessing from a hyperlink/refresh page

# v1.16.1 / Tue Jun 07 2022 13:16:21 GMT+0000 (UTC)

- fix(Site: Magazine): Fixes bug where single page view doesn't work on mobile
- (bug) Executives: Admin user can now update executives

# v1.16.0 / Mon May 30 2022 15:06:36 GMT+0000 (UTC)

- feat(site:performance): Card grids now serve webps

# v1.15.0 / Mon May 30 2022 14:29:25 GMT+0000 (UTC)

- fix(site:widget): Simple Article grid - ensure the show more results display date and address
- feat:(cms: inline image widget): Adds multiple images to inline image widget
- feat(cms: loading bar): Removes loading bar on successful upload
- feat:(cms: article list): Adds authors and content type to list view and makes them filterable
- feat:(cms: display date): Display date updates on each publish to current date time
- feat:(cms: companies): Adds modal to encourage creation of executive on new companies
- feat:(api: content): Updates images to be webp and restrict them to landscape

# v1.14.1 / Mon May 23 2022 14:42:06 GMT+0000 (UTC)

- fix: update salesforce form values

# v1.14.0 / Tue May 17 2022 13:14:46 GMT+0000 (UTC)

- feat(Site: RSS Feed): adds rss feed endpoint

# v1.13.0 / Thu May 05 2022 11:04:41 GMT+0000 (UTC)

- bug:(articles: timepicker): Adds time to date picker
- fix: List Aggregator - ensure manual list deduper works as expected
- feat(global-navigation): Items now open in new tab
- feature(magazine-viewer): Adds new design to magazine view selector
- feat(community-dropdown): Now shows all brands regardless of brand type

# v1.12.0 / Tue Apr 26 2022 09:41:59 GMT+0000 (UTC)

- fix(Paywall): Replaces hard coded message of the brand name in the paywall to dynamic for current magazine site
- User will see less pixelated images on the site
- feat: Allow redirects to be set as permanent

# v1.11.1 / Wed Mar 23 2022 09:43:31 GMT+0000 (UTC)

- fix: Ensure list limit is the lower of the widget or lists limit value

# v1.11.0 / Tue Mar 22 2022 11:20:37 GMT+0000 (UTC)

- Performance enhancements
- feat(themes): Add new brand themes

# v1.10.1 / Fri Mar 18 2022 12:40:30 GMT+0000 (UTC)

- fix(lcp): Preload BasicImage to improve LCP score

# v1.10.0 / Wed Mar 16 2022 09:55:38 GMT+0000 (UTC)

- [WIP] Performance enhancements

# v1.9.2 / Tue Mar 15 2022 09:58:08 GMT+0000 (UTC)

- Adds row gap to partners on articles
- feat(cms:author): Add select component that extends ItemSelector
- fix(admin:ArticleImageUploader): Re-enables Processing animation when…
- Fixes sort on selectize

# v1.9.1 / Tue Mar 08 2022 17:38:46 GMT+0000 (UTC)

- fix: ensure social share context is auto checked

# v1.9.0 / Tue Mar 08 2022 15:34:07 GMT+0000 (UTC)

- fix(site:Homepage): Fixes stretched image bug in Magazine CTA component
- User can now manually set an og:image for each page

# v1.8.1 / Mon Mar 07 2022 09:00:09 GMT+0000 (UTC)

- Fix empty display dates breaking sitemap generation
- fix(sitemap): Don't generate empty article sitemap files when no arti…

# v1.8.0 / Tue Mar 01 2022 09:03:05 GMT+0000 (UTC)

- Updates size of title according to Figma design
- feat: Disable region picker and enable http caching
- feat: Allow inline images to have a free crop
- feat(navigation): Add advertise and editorial links to mobile navigation
- feat(issuu-viewer): Add full screen option on desktop
- Feature/performance enhancements

# v1.7.3 / Fri Feb 18 2022 11:16:02 GMT+0000 (UTC)

- fix: ensure copied links have target=\_blank applied on CKEDITOR
- fix: fix 500 error on company reports with missing images

# v1.7.2 / Mon Feb 14 2022 14:06:26 GMT+0000 (UTC)

- fix(paywall): Ensure correct fonts display
- fix(signup): Fix fonts and spacing
- fix(latest-magazine): Remove truncation of description
- Adds scroll to signup modal
- Prevents iframe from overflowing off page
- Bug/mobile font sizes

# v1.7.1 / Thu Feb 10 2022 17:20:31 GMT+0000 (UTC)

- fix: script to correct media list images
- fix: trim whitepsace on input fields in the CMS
- fix: Don't create article redirect if the article changes instance
- fix: Upgrade CKEditor to fix paste from google docs issue
- fix(global-navigation): Only show B2B brands in community dropdown

# v1.7.0 / Thu Feb 10 2022 08:31:23 GMT+0000 (UTC)

- feat: March 8

# v1.6.0 / Tue Feb 08 2022 08:34:10 GMT+0000 (UTC)

- feat: Migrate publication IDs for certain mags

# v1.5.1 / Mon Feb 07 2022 16:38:59 GMT+0000 (UTC)

- fix: Rename brochure title

# v1.5.0 / Mon Feb 07 2022 14:55:28 GMT+0000 (UTC)

- fix: correct ID used by advert widget
- fix: typo
- fix: Prevent page breaking due to missing component
- fix(type): Fix regressed display0 and heading0 classes
- feat: remove inline image widgets from migrated interviews

# v1.4.0 / Mon Feb 07 2022 14:48:04 GMT+0000 (UTC)

- fix: correct ID used by advert widget
- fix: typo
- fix: Prevent page breaking due to missing component
- feat: remove inline image widgets from migrated interviews

# v1.3.0 / Mon Feb 07 2022 09:43:43 GMT+0000 (UTC)

- fix: ensure robots.txt correctly disallows bots on /search with querystrings
- feat: disable force paste as plain text
- feat: Redirect to paths and include querystring in some situations

# v1.2.0 / Thu Feb 03 2022 12:49:31 GMT+0000 (UTC)

- fix: Remove inline image widgets from migrated company reports

# v1.1.0 / Thu Feb 03 2022 11:49:29 GMT+0000 (UTC)

- fix: Update content feed to remove prefixed / on images

# v1.0.1 / Thu Feb 03 2022 11:14:15 GMT+0000 (UTC)

- fix: Add validation to prevent corrupt crops on inline image widget
- fix: Ensure company exec pages don't error

# v1.0.0 / Tue Feb 01 2022 08:24:05 GMT+0000 (UTC)

- fix(textures): Convert to png
- fix: Ensure article page ad slot names are correct
- feat: Allow Custom Author name on articles

# v0.1.0 / Mon Jan 31 2022 09:01:07 GMT+0000 (UTC)

- fix(opengraph): Add opengraph image data for magazine issues and brochures
- fix(article-header): Ensure article headlines use h1 element
- fix: remove react moment usage
- fix: Defer next scripts
- fix: Cache static content longer
- fix(type): Simplify component to reduce DOM nodes
- feat(advertise): Update lead source value

# v0.0.2 / Fri Jan 28 2022 08:24:08 GMT+0000 (UTC)

- fix: Improvements to content API

# v0.0.1 / Thu Jan 27 2022 15:33:12 GMT+0000 (UTC)

- Bug/search performance gains
